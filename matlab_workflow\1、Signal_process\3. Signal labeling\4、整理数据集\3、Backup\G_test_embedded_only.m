%% G_TEST_EMBEDDED_ONLY 测试仅使用embedded模式（无需Raw data文件夹）
%   验证在embedded模式下，是否可以仅依赖4、Label文件夹中的ls_data3.mat文件
%   而无需1、Raw data文件夹中的原始数据文件
%
%   测试目标：
%   1. 验证labeledSignalSet对象包含完整的信号数据
%   2. 测试在没有外部文件的情况下能否正常提取信号片段
%   3. 确认embedded模式的独立性和完整性
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：1.0

clear;
clc;
close all;

fprintf('=== 测试embedded模式的独立性 ===\n\n');

%% 配置参数
config = struct();
config.labelFile = fullfile('4、Label', 'ls_data3.mat');
config.outputDir = '2、Processed data';
config.testOutputDir = fullfile(config.outputDir, 'embedded_only_test');

% 创建测试输出目录
if ~exist(config.testOutputDir, 'dir')
    mkdir(config.testOutputDir);
end

%% 加载标注文件
fprintf('1. 加载标注文件: %s\n', config.labelFile);

if ~exist(config.labelFile, 'file')
    error('标注文件不存在: %s', config.labelFile);
end

try
    load(config.labelFile, 'ls');
    fprintf('   ✓ 标注文件加载成功\n');
catch ME
    error('标注文件加载失败: %s', ME.message);
end

%% 验证数据完整性
fprintf('\n2. 验证数据完整性\n');

labels = ls.Labels;
sources = ls.Source;

fprintf('   Labels数量: %d\n', height(labels));
fprintf('   Sources数量: %d\n', length(sources));

% 检查数据一致性
if height(labels) ~= length(sources)
    error('标注表和信号源数量不匹配');
end

% 检查每个Source的数据完整性
allSourcesValid = true;
for i = 1:length(sources)
    if isempty(sources{i}) || height(sources{i}) == 0
        fprintf('   ❌ Source{%d} 数据为空\n', i);
        allSourcesValid = false;
    end
end

if allSourcesValid
    fprintf('   ✓ 所有Source数据完整\n');
else
    error('部分Source数据不完整，无法进行embedded模式测试');
end

%% 测试信号提取（仅使用embedded数据）
fprintf('\n3. 测试信号提取（仅使用embedded数据）\n');

extractedCount = 0;
totalLabels = 0;

for i = 1:height(labels)
    signalName = labels.Row{i};
    bsTable = labels{i, 'BS'}{1};
    
    % 跳过无标注的信号
    if isempty(bsTable) || height(bsTable) == 0
        continue;
    end
    
    fprintf('   处理信号: %s\n', signalName);
    
    % 解析信号名称
    try
        [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
    catch ME
        warning('无法解析信号名称 %s: %s', signalName, ME.message);
        continue;
    end
    
    % 直接从labeledSignalSet提取信号数据（embedded模式）
    try
        signalData = sources{i};
        fprintf('     ✓ 从labeledSignalSet提取信号数据成功\n');
        fprintf('     信号大小: %dx%d\n', height(signalData), width(signalData));
        fprintf('     时间范围: %.3f - %.3f 秒\n', ...
            seconds(signalData.Time(1)), seconds(signalData.Time(end)));
    catch ME
        warning('从labeledSignalSet提取信号失败: %s', ME.message);
        continue;
    end
    
    % 处理每个标注
    for j = 1:height(bsTable)
        totalLabels = totalLabels + 1;
        
        try
            % 获取标注信息
            roiLimits = bsTable.ROILimits(j, :);
            labelValue = bsTable.Value(j);
            if iscell(labelValue)
                labelValue = labelValue{1};
            end
            
            fprintf('     标注 %d: ROI=[%.3f,%.3f], 标签=%s\n', ...
                j, roiLimits(1), roiLimits(2), labelValue);
            
            % 提取信号片段
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
            
            % 计算还原时间
            segmentOffset = (segmentNum - 1) * 60;
            realStartTime = roiLimits(1) + segmentOffset;
            realEndTime = roiLimits(2) + segmentOffset;
            
            % 创建还原时间的信号
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);
            restoredSignal = extractedSignal;
            restoredSignal.Time = restoredTime;
            
            % 准备保存的数据结构
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;
            extractedData.restoredSignal = restoredSignal;
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [realStartTime, realEndTime];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;
            extractedData.labelInfo.dataLoadMode = 'embedded_only';
            extractedData.labelInfo.testMode = true;
            extractedData.labelInfo.processingTime = datetime('now');
            
            % 保存测试文件
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d_embedded_only.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(config.testOutputDir, outputFileName);
            
            save(outputFilePath, 'extractedData');
            extractedCount = extractedCount + 1;
            
            fprintf('       ✓ 已保存: %s\n', outputFileName);
            fprintf('       提取的信号长度: %d 个采样点\n', height(extractedSignal));
            fprintf('       原始时间范围: %.3f - %.3f 秒\n', ...
                seconds(extractedSignal.Time(1)), seconds(extractedSignal.Time(end)));
            fprintf('       还原时间范围: %.3f - %.3f 秒\n', ...
                seconds(restoredSignal.Time(1)), seconds(restoredSignal.Time(end)));
            
        catch ME
            warning('提取标注 %d 时出错: %s', j, ME.message);
        end
    end
end

%% 生成测试报告
fprintf('\n=== 测试结果 ===\n');
fprintf('总标注数量: %d\n', totalLabels);
fprintf('成功提取: %d\n', extractedCount);
fprintf('成功率: %.1f%%\n', (extractedCount/max(totalLabels,1))*100);
fprintf('测试输出目录: %s\n', config.testOutputDir);

%% 验证结论
fprintf('\n=== 结论 ===\n');
if extractedCount == totalLabels && totalLabels > 0
    fprintf('✅ 测试成功！embedded模式可以完全独立工作\n');
    fprintf('✅ 无需1、Raw data文件夹，仅需4、Label/ls_data3.mat文件\n');
    fprintf('✅ labeledSignalSet对象包含完整的原始信号数据\n');
    fprintf('✅ 可以正常提取信号片段并进行时间轴还原\n');
    
    fprintf('\n📋 实际应用建议：\n');
    fprintf('   - 在embedded模式下，可以删除或移动1、Raw data文件夹\n');
    fprintf('   - 只需保留4、Label文件夹中的ls_data3.mat文件\n');
    fprintf('   - 这将显著减少存储空间需求\n');
    fprintf('   - 简化文件管理和部署流程\n');
    
elseif totalLabels == 0
    fprintf('⚠️  没有找到标注数据进行测试\n');
    fprintf('   但labeledSignalSet数据结构完整，理论上embedded模式可以独立工作\n');
else
    fprintf('❌ 测试失败，embedded模式无法完全独立工作\n');
    fprintf('   成功率: %.1f%% (期望: 100%%)\n', (extractedCount/totalLabels)*100);
end

%% 存储空间分析
fprintf('\n=== 存储空间分析 ===\n');

% 检查labeledSignalSet文件大小
labelFileInfo = dir(config.labelFile);
fprintf('labeledSignalSet文件大小: %.2f MB\n', labelFileInfo.bytes/1024/1024);

% 检查Raw data文件夹大小（如果存在）
rawDataDir = '1、Raw data';
if exist(rawDataDir, 'dir')
    rawDataFiles = dir(fullfile(rawDataDir, '*.mat'));
    totalRawDataSize = sum([rawDataFiles.bytes]);
    fprintf('Raw data文件夹大小: %.2f MB (%d个文件)\n', ...
        totalRawDataSize/1024/1024, length(rawDataFiles));
    
    if totalRawDataSize > 0
        spaceSaving = (totalRawDataSize / (totalRawDataSize + labelFileInfo.bytes)) * 100;
        fprintf('使用embedded模式可节省存储空间: %.1f%%\n', spaceSaving);
    end
else
    fprintf('Raw data文件夹不存在\n');
end

fprintf('\n测试完成！\n');

%% 辅助函数
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');
    
    if isempty(tokens)
        error('无法解析信号名称: %s', signalName);
    end
    
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end
