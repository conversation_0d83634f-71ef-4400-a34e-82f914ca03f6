%% 测试优化的数据提取方法
clear; clc;

fprintf('=== 测试优化的数据提取方法 ===\n');

% 配置
labelFile = '4、Label/ls_data3.mat';
outputDir = '2、Processed data';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
end

% 加载标注文件
fprintf('加载标注文件: %s\n', labelFile);
load(labelFile);

labels = ls.Labels;
sources = ls.Source;
fprintf('找到 %d 个信号文件\n', height(labels));

% 处理有标注的信号
extractedCount = 0;
totalLabels = 0;

for i = 1:height(labels)
    signalName = labels.Row{i};
    bsTable = labels{i, 'BS'}{1};
    
    if isempty(bsTable) || height(bsTable) == 0
        continue;
    end
    
    fprintf('\n处理信号: %s (%d个标注)\n', signalName, height(bsTable));
    
    % 解析信号名称
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');
    
    if isempty(tokens)
        warning('无法解析信号名称: %s', signalName);
        continue;
    end
    
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
    
    % 从labeledSignalSet直接获取信号数据
    signalData = sources{i};
    
    % 处理每个标注
    for j = 1:height(bsTable)
        totalLabels = totalLabels + 1;
        
        roiLimits = bsTable.ROILimits(j, :);
        labelValue = bsTable.Value(j);
        if iscell(labelValue)
            labelValue = labelValue{1};
        end
        
        % 计算还原时间
        segmentOffset = (segmentNum - 1) * 60;
        realStartTime = roiLimits(1) + segmentOffset;
        realEndTime = roiLimits(2) + segmentOffset;
        
        fprintf('  标注 %d: %.3f-%.3f秒 -> %.3f-%.3f秒, 标签: %s\n', ...
            j, roiLimits(1), roiLimits(2), realStartTime, realEndTime, labelValue);
        
        try
            % 提取信号片段
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
            
            % 创建还原时间的信号
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);
            restoredSignal = extractedSignal;
            restoredSignal.Time = restoredTime;
            
            % 准备保存的数据
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;
            extractedData.restoredSignal = restoredSignal;
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [realStartTime, realEndTime];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;
            extractedData.labelInfo.dataLoadMode = 'embedded';
            
            % 保存文件
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d_opt.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(outputDir, outputFileName);
            
            save(outputFilePath, 'extractedData');
            extractedCount = extractedCount + 1;
            
            fprintf('    已保存: %s\n', outputFileName);
            
        catch ME
            warning('提取失败: %s', ME.message);
        end
    end
end

fprintf('\n=== 处理完成 ===\n');
fprintf('总标注数量: %d\n', totalLabels);
fprintf('成功提取: %d\n', extractedCount);
fprintf('成功率: %.1f%%\n', (extractedCount/max(totalLabels,1))*100);
