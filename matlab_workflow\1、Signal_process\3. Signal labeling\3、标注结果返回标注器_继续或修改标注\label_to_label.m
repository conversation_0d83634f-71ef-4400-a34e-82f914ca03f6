function [labelVals, labelLocs] = label_to_label(~, ~, ~, ~, varargin)
%LABEL_TO_LABEL 从标注文件中提取指定文件的标注信息
%   从已有的标注文件中查找指定文件的标注信息，提取标签值和时间位置，
%   并进行统计分析。主要用于肠鸣音信号分析中的标注数据提取。
%
%   Syntax:
%   [labelVals, labelLocs] = label_to_label(x, t, parentLabelVal, parentLabelLoc)
%   [labelVals, labelLocs] = label_to_label(x, t, parentLabelVal, parentLabelLoc, Fs)
%   [labelVals, labelLocs] = label_to_label(x, t, parentLabelVal, parentLabelLoc, Fs, labelFileName)
%   [labelVals, labelLocs] = label_to_label(x, t, parentLabelVal, parentLabelLoc, Fs, labelFileName, targetFile)
%
%   Inputs:
%   x - 信号数据 (double array, 当前版本未使用，保留用于接口兼容性)
%   t - 时间数据 (double array, 当前版本未使用，保留用于接口兼容性)
%   parentLabelVal - 父标签值 (string/char, 当前版本未使用，保留用于接口兼容性)
%   parentLabelLoc - 父标签位置 (double array, 当前版本未使用，保留用于接口兼容性)
%   Fs - 采样率 (double, 可选参数, 默认值: 2570 Hz)
%   labelFileName - 标注文件名 (string/char, 可选参数, 默认值: 'ls_data3.mat')   需要指定
%   targetFile - 目标文件名 (string/char, 可选参数, 默认值: 'data3_5min_seg002_tt1')   需要指定
%
%   Outputs:
%   labelVals - 标签值字符串数组 (string array, 包含SB/MB/CRS等标注类型)
%   labelLocs - 标签时间位置矩阵 (double array, Nx2, [开始时间, 结束时间])
%
%   Example:
%   % 基本用法（使用默认参数）
%   [labels, locations] = label_to_label([], [], [], []);
%
%   % 指定采样率
%   [labels, locations] = label_to_label([], [], [], [], 2570);
%
%   % 指定标注文件和目标文件
%   [labels, locations] = label_to_label([], [], [], [], 2570, 'ls_data3.mat', 'data1_5min_seg001_tt');
%
%   Algorithm:
%   1. 解析输入参数，设置默认值
%   2. 从'4、Label/'文件夹加载指定的标注文件，获取标注信息表
%   3. 遍历文件列表，查找目标文件（支持双通道命名规则）
%   4. 提取目标文件的标注值(SB/MB/CRS)和时间范围
%   5. 统计各类型标注数量并显示详细信息
%   6. 返回格式化的标签值和位置数组
%
%   Notes:
%   - 标注文件从'4、Label/'文件夹中加载
%   - 支持双通道数据文件的命名规则（如data1_5min_seg001_tt1和data1_5min_seg001_tt2）
%   - 前四个输入参数保留用于接口兼容性，但在当前版本中未被使用
%   - 支持的标注类型：SB(单次肠鸣音)、MB(多次肠鸣音)、CRS(持续肠鸣音)
%   - 文件匹配使用模糊匹配，支持部分文件名匹配
%
%   See also: LOAD, ISTABLE, FILEPARTS, FULLFILE

    % 初始化输出变量
    labelVals = string([]);
    labelLocs = zeros(0,2);

    % 解析输入参数
    % 设置默认采样率
    if nargin < 5 || isempty(varargin{1})
        Fs = 2570;
    else
        Fs = varargin{1};
    end

    % 设置默认标注文件名
    if nargin < 6 || isempty(varargin{2})
        labelFileName = 'ls_data3.mat';
    else
        labelFileName = varargin{2};
    end

    % 设置默认目标文件名
    if nargin < 7 || isempty(varargin{3})
        targetFile = 'data3_5min_seg002_tt1';  % 更新为存在的文件
    else
        targetFile = varargin{3};
    end

    % 参数验证
    if ~ischar(labelFileName) && ~isstring(labelFileName)
        error('标注文件名必须是字符串或字符数组');
    end
    if ~ischar(targetFile) && ~isstring(targetFile)
        error('目标文件名必须是字符串或字符数组');
    end

    % 转换为字符串格式
    labelFileName = char(labelFileName);
    targetFile = char(targetFile);

    try
        % 构建标注文件的完整路径
        labelFilePath = fullfile('4、Label', labelFileName);

        % 检查标注文件是否存在
        if ~exist(labelFilePath, 'file')
            error('标注文件不存在: %s\n请检查文件路径和文件名是否正确。', labelFilePath);
        end

        % 加载标注文件
        fprintf('正在加载标注文件: %s\n', labelFilePath);
        labelData = load(labelFilePath);

        % 检查标注数据结构
        if ~isfield(labelData, 'ls')
            error('标注文件格式错误: 缺少ls变量\n文件路径: %s', labelFilePath);
        end

        % 获取 labeledSignalSet 对象
        ls = labelData.ls;

        % 检查是否为 labeledSignalSet 对象
        if ~isa(ls, 'labeledSignalSet')
            error('标注文件格式错误: ls不是labeledSignalSet对象\n文件路径: %s\n实际类型: %s', labelFilePath, class(ls));
        end

        % 获取 Labels（对于 labeledSignalSet 对象，直接访问 Labels 属性）
        try
            labels = ls.Labels;
        catch ME
            error('标注文件格式错误: 无法访问labeledSignalSet的Labels属性\n文件路径: %s\n错误信息: %s', labelFilePath, ME.message);
        end

        % 验证Labels结构
        if ~istable(labels)
            error('标注文件格式错误: Labels不是table格式\n文件路径: %s', labelFilePath);
        end

        % 检查Labels表格结构
        if width(labels) == 0 || height(labels) == 0
            error('标注文件格式错误: Labels表格为空\n文件路径: %s', labelFilePath);
        end

        % 获取行名（文件名）和第一列数据（BS表）
        filePaths = labels.Properties.RowNames;  % 使用RowNames获取文件名

        % 检查是否有BS列
        if any(strcmp(labels.Properties.VariableNames, 'BS'))
            bsTables = labels.BS;  % 直接访问BS列
        else
            % 如果没有BS列，使用第一列
            bsTables = labels{:, 1};
        end

        fprintf('标注文件加载成功，包含 %d 个文件的标注信息\n', numel(bsTables));
        fprintf('正在查找目标文件: %s\n', targetFile);

        % 遍历每个文件，查找目标文件
        found = false;
        for i = 1:numel(bsTables)
            % 获取当前文件路径和文件名
            currentFilePath = filePaths{i};
            [~, fileName, ~] = fileparts(currentFilePath);

            % 改进的文件名匹配逻辑，支持双通道命名规则
            % 移除可能的通道后缀（tt1, tt2）进行匹配
            baseFileName = regexprep(fileName, '(tt1|tt2)$', 'tt');
            targetFileBase = regexprep(targetFile, '(tt1|tt2)$', 'tt');

            % 检查是否为目标文件（支持模糊匹配）
            if contains(baseFileName, targetFileBase) || contains(targetFileBase, baseFileName)
                currentBsTable = bsTables{i};

                % 验证当前 BS 表是否为 table
                if istable(currentBsTable) && height(currentBsTable) > 0
                    % 打印文件信息
                    fprintf('\n✓ 找到目标文件: %s\n', fileName);
                    fprintf('  原始文件路径: %s\n', currentFilePath);
                    fprintf('  基础文件名: %s\n', baseFileName);
                    fprintf('  标注总数: %d\n\n', height(currentBsTable));

                    % 验证表格结构
                    if ~any(strcmp(currentBsTable.Properties.VariableNames, 'Value'))
                        warning('标注表格缺少Value列，跳过文件: %s', fileName);
                        continue;
                    end
                    if ~any(strcmp(currentBsTable.Properties.VariableNames, 'ROILimits'))
                        warning('标注表格缺少ROILimits列，跳过文件: %s', fileName);
                        continue;
                    end

                    % 打印标注详细信息
                    fprintf('标注详情:\n');
                    fprintf('序号\t类型\t开始时间(s)\t结束时间(s)\t持续时间(ms)\n');
                    fprintf('----------------------------------------------------\n');

                    % 统计各类型数量
                    sb_count = 0;
                    mb_count = 0;
                    crs_count = 0;
                    other_count = 0;

                    % 获取所有标注值和位置
                    values = table2array(currentBsTable(:, 'Value'));
                    locations = table2array(currentBsTable(:, 'ROILimits'));

                    % 遍历并处理每个标注
                    for j = 1:height(currentBsTable)
                        % 获取当前标注信息
                        roiLimits = locations(j, :);
                        labelValue = values(j);

                        % 计算持续时间（毫秒）
                        duration_ms = (roiLimits(2) - roiLimits(1)) * 1000;

                        % 打印标注信息
                        fprintf('%d\t%s\t%.3f\t\t%.3f\t\t%.1f\n', ...
                            j, labelValue, roiLimits(1), roiLimits(2), duration_ms);

                        % 更新计数
                        switch char(labelValue)
                            case 'SB'
                                sb_count = sb_count + 1;
                            case 'MB'
                                mb_count = mb_count + 1;
                            case 'CRS'
                                crs_count = crs_count + 1;
                            otherwise
                                other_count = other_count + 1;
                        end
                    end

                    % 打印统计信息
                    fprintf('\n标注统计:\n');
                    fprintf('SB (单次肠鸣音): %d\n', sb_count);
                    fprintf('MB (多次肠鸣音): %d\n', mb_count);
                    fprintf('CRS (持续肠鸣音): %d\n', crs_count);
                    if other_count > 0
                        fprintf('其他类型: %d\n', other_count);
                    end
                    fprintf('总计: %d\n', height(currentBsTable));

                    % 设置返回值
                    labelVals = string(values);
                    labelLocs = locations;
                    found = true;
                    break;
                elseif ~istable(currentBsTable)
                    fprintf('警告: 文件 %s 的标注数据不是table格式，跳过\n', fileName);
                else
                    fprintf('警告: 文件 %s 的标注数据为空，跳过\n', fileName);
                end
            end
        end
        
        % 如果没有找到目标文件
        if ~found
            fprintf('\n❌ 未找到目标文件的标注信息\n');
            fprintf('  目标文件: %s\n', targetFile);
            fprintf('  标注文件: %s\n', labelFilePath);
            fprintf('  可用文件列表:\n');
            for k = 1:min(10, numel(filePaths))  % 最多显示10个文件
                [~, availableFileName, ~] = fileparts(filePaths{k});
                fprintf('    %d. %s\n', k, availableFileName);
            end
            if numel(filePaths) > 10
                fprintf('    ... 还有 %d 个文件\n', numel(filePaths) - 10);
            end
            warning('未找到文件 %s 的标注信息。请检查目标文件名是否正确。', targetFile);
        end

    catch ME
        % 错误处理
        fprintf('\n❌ 处理过程出现错误\n');
        fprintf('  错误信息: %s\n', ME.message);
        fprintf('  错误标识: %s\n', ME.identifier);
        if ~isempty(ME.stack)
            fprintf('  错误位置: %s，第 %d 行\n', ME.stack(1).name, ME.stack(1).line);
        end

        % 根据错误类型提供具体建议
        if contains(ME.message, '不存在')
            fprintf('  建议: 检查标注文件路径和文件名是否正确\n');
        elseif contains(ME.message, 'ls结构体')
            fprintf('  建议: 检查标注文件是否包含正确的ls结构体\n');
        elseif contains(ME.message, 'Labels字段')
            fprintf('  建议: 检查ls结构体是否包含Labels字段\n');
        end

        warning(ME.identifier, '处理过程出现错误：%s', ME.message);
    end

    % 确保输出格式正确
    if isempty(labelVals)
        labelVals = string([]);
        labelLocs = zeros(0,2);
    elseif iscell(labelVals)
        labelVals = string(labelVals);
    end

    % 最终状态报告
    if ~isempty(labelVals)
        fprintf('\n✓ 标注信息提取完成\n');
        fprintf('  提取的标注数量: %d\n', length(labelVals));
        fprintf('  时间范围: %.3f - %.3f 秒\n', min(labelLocs(:,1)), max(labelLocs(:,2)));
    else
        fprintf('\n⚠ 未提取到任何标注信息\n');
    end
end