%% 自动化工具功能测试脚本
%TEST_AUTOMATION_TOOLS 测试新创建的自动化工具的基本功能
%   本脚本用于验证workflow_controller、file_manager和config_manager
%   的基本功能是否正常工作，确保工具可以正常使用。
%
%   作者: Augment Agent
%   日期: 2024-08-22
%   版本: 1.0

function test_automation_tools()
    fprintf('=== 自动化工具功能测试 ===\n\n');
    
    % 测试结果统计
    totalTests = 0;
    passedTests = 0;
    failedTests = 0;
    
    %% 测试1: 配置管理器基本功能
    fprintf('测试1: 配置管理器基本功能\n');
    fprintf('==========================\n');
    
    try
        totalTests = totalTests + 1;
        
        % 测试配置加载
        fprintf('测试配置加载...\n');
        config = config_manager('load');
        assert(isstruct(config), '配置应该是结构体');
        fprintf('✓ 配置加载成功\n');
        
        % 测试配置验证
        fprintf('测试配置验证...\n');
        isValid = config_manager('validate', config);
        assert(islogical(isValid), '验证结果应该是逻辑值');
        fprintf('✓ 配置验证功能正常\n');
        
        % 测试获取配置值
        fprintf('测试获取配置值...\n');
        fs = config_manager('get', 'global_settings.sampling_rate');
        assert(isnumeric(fs) && fs > 0, '采样率应该是正数');
        fprintf('✓ 获取配置值成功: %d Hz\n', fs);
        
        passedTests = passedTests + 1;
        fprintf('✓ 配置管理器测试通过\n\n');
        
    catch ME
        failedTests = failedTests + 1;
        fprintf('✗ 配置管理器测试失败: %s\n\n', ME.message);
    end
    
    %% 测试2: 文件管理器基本功能
    fprintf('测试2: 文件管理器基本功能\n');
    fprintf('==========================\n');
    
    try
        totalTests = totalTests + 1;
        
        % 测试检测输出文件
        fprintf('测试检测输出文件...\n');
        outputFiles = file_manager('detect_outputs', 0);
        assert(iscell(outputFiles), '输出文件列表应该是cell数组');
        fprintf('✓ 检测到 %d 个输出文件\n', length(outputFiles));
        
        % 测试生成文件报告
        fprintf('测试生成文件报告...\n');
        report = file_manager('generate_report', 0);
        assert(isstruct(report), '报告应该是结构体');
        assert(isfield(report, 'stage'), '报告应该包含stage字段');
        fprintf('✓ 文件报告生成成功\n');
        
        % 测试文件验证（使用空列表）
        fprintf('测试文件验证功能...\n');
        result = file_manager('validate_files', {});
        assert(isstruct(result), '验证结果应该是结构体');
        assert(isfield(result, 'total_count'), '验证结果应该包含total_count字段');
        fprintf('✓ 文件验证功能正常\n');
        
        passedTests = passedTests + 1;
        fprintf('✓ 文件管理器测试通过\n\n');
        
    catch ME
        failedTests = failedTests + 1;
        fprintf('✗ 文件管理器测试失败: %s\n\n', ME.message);
    end
    
    %% 测试3: 工作流程控制器参数解析
    fprintf('测试3: 工作流程控制器参数解析\n');
    fprintf('==============================\n');
    
    try
        totalTests = totalTests + 1;
        
        % 测试预演模式
        fprintf('测试预演模式...\n');
        workflow_controller('dry_run', true, 'start_stage', 0, 'end_stage', 1);
        fprintf('✓ 预演模式执行成功\n');
        
        passedTests = passedTests + 1;
        fprintf('✓ 工作流程控制器测试通过\n\n');
        
    catch ME
        failedTests = failedTests + 1;
        fprintf('✗ 工作流程控制器测试失败: %s\n\n', ME.message);
    end
    
    %% 测试4: 配置文件完整性
    fprintf('测试4: 配置文件完整性\n');
    fprintf('====================\n');
    
    try
        totalTests = totalTests + 1;
        
        % 检查配置文件是否存在
        fprintf('检查配置文件存在性...\n');
        configFile = 'workflow_config.json';
        assert(exist(configFile, 'file') == 2, '配置文件应该存在');
        fprintf('✓ 配置文件存在: %s\n', configFile);
        
        % 检查配置文件格式
        fprintf('检查配置文件格式...\n');
        fid = fopen(configFile, 'r');
        assert(fid ~= -1, '应该能够打开配置文件');
        content = fread(fid, inf, 'char=>char')';
        fclose(fid);
        
        config_data = jsondecode(content);
        assert(isstruct(config_data), '配置文件应该是有效的JSON格式');
        fprintf('✓ 配置文件格式正确\n');
        
        % 检查必需字段
        fprintf('检查必需字段...\n');
        requiredFields = {'workflow_info', 'global_settings', 'stage_settings', 'file_paths'};
        for i = 1:length(requiredFields)
            assert(isfield(config_data, requiredFields{i}), ...
                sprintf('配置文件应该包含字段: %s', requiredFields{i}));
        end
        fprintf('✓ 必需字段检查通过\n');
        
        passedTests = passedTests + 1;
        fprintf('✓ 配置文件完整性测试通过\n\n');
        
    catch ME
        failedTests = failedTests + 1;
        fprintf('✗ 配置文件完整性测试失败: %s\n\n', ME.message);
    end
    
    %% 测试5: 文件系统权限
    fprintf('测试5: 文件系统权限\n');
    fprintf('==================\n');
    
    try
        totalTests = totalTests + 1;
        
        % 测试创建临时文件
        fprintf('测试文件创建权限...\n');
        tempFile = 'test_temp_file.txt';
        fid = fopen(tempFile, 'w');
        assert(fid ~= -1, '应该能够创建临时文件');
        fprintf(fid, 'test content');
        fclose(fid);
        fprintf('✓ 文件创建权限正常\n');
        
        % 测试文件读取
        fprintf('测试文件读取权限...\n');
        fid = fopen(tempFile, 'r');
        assert(fid ~= -1, '应该能够读取临时文件');
        content = fread(fid, inf, 'char=>char')';
        fclose(fid);
        assert(strcmp(content, 'test content'), '文件内容应该正确');
        fprintf('✓ 文件读取权限正常\n');
        
        % 清理临时文件
        fprintf('清理临时文件...\n');
        delete(tempFile);
        assert(~exist(tempFile, 'file'), '临时文件应该被删除');
        fprintf('✓ 文件删除权限正常\n');
        
        passedTests = passedTests + 1;
        fprintf('✓ 文件系统权限测试通过\n\n');
        
    catch ME
        failedTests = failedTests + 1;
        fprintf('✗ 文件系统权限测试失败: %s\n\n', ME.message);
    end
    
    %% 测试6: 错误处理机制
    fprintf('测试6: 错误处理机制\n');
    fprintf('==================\n');
    
    try
        totalTests = totalTests + 1;
        
        % 测试无效参数处理
        fprintf('测试无效参数处理...\n');
        try
            config_manager('invalid_action');
            assert(false, '应该抛出错误');
        catch ME
            assert(contains(ME.message, '未知的操作类型'), '应该是未知操作类型错误');
            fprintf('✓ 无效参数错误处理正常\n');
        end
        
        % 测试文件不存在处理
        fprintf('测试文件不存在处理...\n');
        result = file_manager('validate_files', {'nonexistent_file.mat'});
        assert(result.missing_count == 1, '应该检测到1个缺失文件');
        fprintf('✓ 文件不存在错误处理正常\n');
        
        passedTests = passedTests + 1;
        fprintf('✓ 错误处理机制测试通过\n\n');
        
    catch ME
        failedTests = failedTests + 1;
        fprintf('✗ 错误处理机制测试失败: %s\n\n', ME.message);
    end
    
    %% 测试结果汇总
    fprintf('=== 测试结果汇总 ===\n');
    fprintf('总测试数: %d\n', totalTests);
    fprintf('通过测试: %d\n', passedTests);
    fprintf('失败测试: %d\n', failedTests);
    
    if failedTests == 0
        fprintf('\n🎉 所有测试通过！自动化工具可以正常使用。\n');
        fprintf('建议运行 workflow_demo.m 查看详细使用示例。\n');
    else
        fprintf('\n⚠️  有 %d 个测试失败，请检查相关功能。\n', failedTests);
        fprintf('建议查看错误信息并修复相关问题。\n');
    end
    
    fprintf('\n测试完成时间: %s\n', datestr(now));
end

%% 辅助函数
function runSafeTest(testName, testFunc)
%RUNSAFETEST 安全运行测试函数
    try
        fprintf('运行测试: %s\n', testName);
        testFunc();
        fprintf('✓ %s 通过\n', testName);
    catch ME
        fprintf('✗ %s 失败: %s\n', testName, ME.message);
    end
end
