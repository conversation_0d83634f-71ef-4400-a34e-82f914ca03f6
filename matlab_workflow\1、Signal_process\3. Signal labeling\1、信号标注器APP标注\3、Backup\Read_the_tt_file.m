function [time, signal, tt1] = Read_the_tt_file()
%READ_THE_TT_FILE 读取和验证timetable格式的数据文件
%   交互式读取包含timetable数据的MAT文件，并提取时间和信号数据。
%   该函数提供用户友好的文件选择界面，并自动验证数据格式的正确性。
%
%   语法:
%   [time, signal, tt1] = Read_the_tt_file()
%
%   输入参数:
%   无 - 函数通过图形界面让用户选择文件
%
%   输出参数:
%   time            - 时间向量 (duration array, N×1)
%                     从timetable中提取的时间数据，已归零处理
%   signal          - 信号数据矩阵 (double array, N×M)
%                     从timetable中提取的信号数据，M为通道数
%   tt1             - 原始timetable (timetable)
%                     完整的原始timetable数据结构
%
%   功能特点:
%   - 支持交互式文件选择
%   - 自动验证timetable格式
%   - 时间数据自动归零处理
%   - 完整的错误检查和用户反馈
%
%   数据要求:
%   - 文件必须为MAT格式
%   - 文件中必须包含名为'tt1'的变量
%   - tt1必须为timetable格式
%
%   示例:
%   % 读取数据文件
%   [timeData, signalData, originalTable] = Read_the_tt_file();
%
%   % 显示数据信息
%   fprintf('数据长度: %d 样本\n', length(timeData));
%   fprintf('信号通道数: %d\n', size(signalData, 2));
%   fprintf('时间范围: %.2f - %.2f 秒\n', seconds(timeData(1)), seconds(timeData(end)));
%
%   注意事项:
%   - 如果用户取消文件选择，函数将返回空值
%   - 函数会清空工作空间，请注意保存重要数据
%   - 时间数据会自动转换为从零开始的duration格式
%
%   参见: UIGETFILE, ISTIMETABLE, LOAD
%
%   作者: 信号处理团队
%   版本: 1.0
%   日期: 2024年

% 清空工作空间并关闭所有图形窗口
clear all;
close all;
clc;

% 初始化输出变量
time = [];
signal = [];
tt1 = [];

% 加载数据文件 - 让用户选择文件
[filename, pathname] = uigetfile('*.mat', '请选择包含timetable数据的.mat文件');

% 检查用户是否取消了文件选择
if isequal(filename, 0)
    disp('用户取消了文件选择');
    return;
end

% 构建完整的文件路径并加载
fullpath = fullfile(pathname, filename);
fprintf('正在加载文件: %s\n', fullpath);
load(fullpath);

% 检查加载的数据中是否存在tt1
if ~exist('tt1', 'var')
    error('在数据文件中找不到tt1');
end

% 确保tt1是timetable格式
if ~istimetable(tt1)
    error('tt1不是timetable格式');
end

% 从timetable中提取时间和信号数据
time = tt1.Time - tt1.Time(1); % 将时间归零
signal = tt1.Variables; % 假设timetable只有一列数据