# 肠鸣音信号标注工作流程详细说明

## 📋 工作流概述

本工作流程包含5个核心步骤，用于完成从原始音频信号到标准化数据集的完整肠鸣音信号标注流程。每个步骤都有明确的输入输出要求和数据流向。

### 🎯 总体目标
- 训练和优化肠鸣音自动检测算法
- 建立标准化的肠鸣音信号数据集
- 实现高效的批量数据处理流程
- 支持标注结果的迭代优化

---

## 📁 文件夹结构分析

### 0、训练标记 - 算法训练与开发
**功能描述**: 开发和训练肠鸣音自动检测算法
**主要文件**:
- `Label_test.m` - 核心自动标注算法（761行代码）
- `audio_preprocessing.m` - 音频预处理工具
- `test_preprocessing.m` - 预处理测试脚本
- `biaoqian.mat` - 训练用标注数据

**检测能力**:
- SB（短脉冲音）: 8-30ms，脉冲特征
- MB（多泡音）: 40-1500ms，多个SB序列
- CRS（连续性声音）: 50-3000ms，连续无间隙

**数据结构**:
```
1、Raw data/          # 原始音频文件（.wav, .pkf, .txt）
2、Processed data/    # 预处理后的分段数据（.mat）
3、Backup/           # 备份文件夹
```

### 1、信号标注器APP标注 - 人工标注工具
**功能描述**: 使用GUI应用程序进行人工标注和文件管理
**主要文件**:
- `BowelSoundLabeler.m` - GUI标注应用程序
- `Label_work.m` - 标注工作脚本
- `Read_the_tt_file.m` - 时间表文件读取工具
- `使用说明.md` - 详细使用指南

**核心功能**:
- 文件夹记忆功能（自动保存常用路径）
- 批量文件标注处理
- 智能文件重命名（_no, _yes_N格式）
- 支持多种音频格式（.mat, .wav, .mp3, .m4a, .flac）

**数据结构**:
```
1、Raw data/          # 待标注的原始文件
2、Processed data/    # 标注后的文件
3、Backup/           # 备份文件夹
```

### 2、标注得到数据集 - 数据提取与整理
**功能描述**: 从标注结果中提取信号片段，构建训练数据集
**主要文件**:
- `label_process_2.m` - 标注数据处理主程序
- `test_extraction_1.m` - 提取功能测试
- `validate_extraction_3.m` - 提取结果验证
- `demo_usage_4.m` - 使用示例和可视化

**处理流程**:
1. 读取标注文件（.mat格式的labeledSignalSet）
2. 根据ROILimits提取对应时间段的信号
3. 按标签类型分类保存信号片段
4. 生成处理报告和统计信息

**数据结构**:
```
1、Raw data/          # 原始分段信号文件
2、Processed data/    # 提取的标注信号片段
3、Backup/           # 备份文件夹
4、Label/            # 标注文件存储
```

### 3、标注结果返回标注器_继续或修改标注 - 迭代优化
**功能描述**: 将处理结果反馈给标注器，支持标注的修改和优化
**主要文件**:
- `label_to_label.m` - 标注信息提取函数（298行代码）
- `check_label_file.m` - 标注文件检查工具

**核心功能**:
- 从已有标注文件中提取指定文件的标注信息
- 支持标注结果的统计分析
- 提供标注质量检查功能
- 支持标注的迭代修改

**数据结构**:
```
1、Raw data/          # 原始信号文件
2、Processed data/    # 处理后的信号文件
3、Backup/           # 备份文件夹
4、Label/            # 标注文件存储
```

### 4、整理数据集 - 批量数据集构建
**功能描述**: 批量处理多个标注文件，构建完整的训练数据集
**主要文件**:
- `multi_label_process_2.m` - 多标注文件批量处理（306行代码）
- `test_multi_label_process_1.m` - 批量处理测试
- `verify_results_3.m` - 结果验证工具

**处理能力**:
- 批量处理4、Label文件夹中的所有标注文件
- 统一的文件命名规则
- 自动提取和分类不同类型的肠鸣音事件
- 生成完整的数据集统计报告

**数据结构**:
```
1、Raw data/          # 原始分段信号文件
2、Processed data/    # 最终的数据集文件
3、Backup/           # 备份文件夹
4、Label/            # 标注文件存储
```

---

## 🔄 完整工作流程

### 阶段1: 算法开发与训练
**位置**: `0、训练标记`
**输入**: 原始音频文件（.wav, .pkf等）
**输出**: 训练好的标注算法和预处理后的分段数据
**操作步骤**:
1. 将原始音频文件放入`1、Raw data/`
2. 运行`audio_preprocessing.m`进行预处理
3. 使用`Label_test.m`训练和测试标注算法
4. 验证算法性能，调整参数

### 阶段2: 人工标注
**位置**: `1、信号标注器APP标注`
**输入**: 预处理后的信号文件
**输出**: 人工标注的信号文件
**操作步骤**:
1. 启动`BowelSoundLabeler()`GUI应用
2. 设置默认工作文件夹
3. 批量选择待标注文件
4. 逐个标注文件（是否包含肠鸣音，数量等）
5. 系统自动重命名文件（_no, _yes_N格式）

### 阶段3: 数据集构建
**位置**: `2、标注得到数据集`
**输入**: 标注后的信号文件和标注信息
**输出**: 结构化的信号片段数据集
**操作步骤**:
1. 将标注文件放入`4、Label/`
2. 将原始分段文件放入`1、Raw data/`
3. 运行`label_process_2.m`提取标注片段
4. 使用`validate_extraction_3.m`验证提取结果
5. 运行`demo_usage_4.m`查看数据集统计

### 阶段4: 标注优化（可选）
**位置**: `3、标注结果返回标注器_继续或修改标注`
**输入**: 已处理的标注数据
**输出**: 优化后的标注信息
**操作步骤**:
1. 使用`check_label_file.m`检查标注质量
2. 运行`label_to_label.m`提取特定文件的标注
3. 根据分析结果返回阶段2进行标注修改
4. 重复优化直到满意

### 阶段5: 批量数据集生成
**位置**: `4、整理数据集`
**输入**: 多个标注文件和原始信号
**输出**: 完整的训练数据集
**操作步骤**:
1. 将所有标注文件放入`4、Label/`
2. 确保`1、Raw data/`包含所有原始分段文件
3. 运行`multi_label_process_2.m`批量处理
4. 使用`verify_results_3.m`验证最终结果
5. 检查`2、Processed data/`中的数据集文件

---

## 📊 数据流向图

```mermaid
graph TD
    A[原始音频文件] --> B[0、训练标记]
    B --> C[预处理分段数据]
    C --> D[1、信号标注器APP标注]
    D --> E[人工标注文件]
    E --> F[2、标注得到数据集]
    F --> G[结构化信号片段]
    G --> H[3、标注结果返回标注器]
    H --> I{标注质量检查}
    I -->|需要优化| D
    I -->|质量满意| J[4、整理数据集]
    J --> K[完整训练数据集]
    
    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style I fill:#fff3e0
```

---

## ⚙️ 技术规格

### 支持的文件格式
- **音频格式**: WAV, MP3, M4A, FLAC
- **数据格式**: MAT (MATLAB数据文件)
- **标注格式**: labeledSignalSet对象

### 信号处理参数
- **默认采样率**: 2570 Hz
- **分段长度**: 60秒
- **频谱分析**: 使用spectrogram函数
- **检测阈值**: 自适应（均值 + N×标准差）

### 标注类型定义
- **SB**: 短脉冲音（8-30ms）
- **MB**: 多泡音（40-1500ms）
- **CRS**: 连续性声音（50-3000ms）
- **HS**: 谐波音（50-1500ms，仅部分版本支持）

---

## 🚀 使用建议

### 首次使用
1. 从`0、训练标记`开始，熟悉算法和数据格式
2. 使用小批量数据测试完整流程
3. 建立标准的文件命名规范
4. 设置合适的备份策略

### 效率优化
1. 使用GUI应用的文件夹记忆功能
2. 批量处理时合理分配文件数量
3. 定期清理临时文件和备份
4. 建立处理日志记录

### 质量控制
1. 每个阶段都进行结果验证
2. 使用提供的测试脚本检查数据完整性
3. 定期备份重要的标注文件
4. 建立标注一致性检查机制

---

## 📝 文件命名规范

### 原始文件
- 格式: `record_YYMMDDXXX_N.ext`
- 示例: `record_080524001_1.wav`

### 分段文件
- 格式: `dataN_5min_segXXX_tt.mat`
- 示例: `data3_5min_seg002_tt.mat`

### 标注文件
- 无肠鸣音: `原文件名_no.ext`
- 有肠鸣音: `原文件名_yes_N.ext` (N为数量)

### 数据集文件
- 格式: `{dataset}_seg{XXX}_{channel}_{label}_{index}.mat`
- 示例: `data3_seg002_tt1_SB_001.mat`

---

## 🔧 故障排除

### 常见问题
1. **文件路径错误**: 检查相对路径设置
2. **内存不足**: 减少批处理文件数量
3. **格式不兼容**: 确认MATLAB版本支持
4. **标注文件损坏**: 使用备份文件恢复

### 性能优化
1. 关闭不必要的可视化功能
2. 使用并行处理工具箱（如可用）
3. 定期清理工作空间变量
4. 优化算法参数减少计算量

---

## 🔍 工作流优化分析与建议

### 当前工作流问题识别

#### 1. 手动文件移动操作过多
**问题**: 每个阶段都需要手动将文件从上一个文件夹复制到下一个文件夹
**影响**:
- 增加操作复杂度和出错概率
- 浪费时间和存储空间
- 容易造成文件版本混乱

#### 2. 数据流向不够自动化
**问题**: 各阶段之间缺乏自动化的数据传递机制
**影响**:
- 需要用户记住复杂的文件路径和命名规则
- 容易遗漏或重复处理文件
- 难以追踪数据处理历史

#### 3. 缺乏统一的配置管理
**问题**: 每个脚本都有独立的参数设置
**影响**:
- 参数不一致可能导致处理结果差异
- 难以批量修改处理参数
- 缺乏全局的质量控制标准

### 🚀 自动化优化方案

#### 方案1: 创建主控制脚本
**建议**: 创建`workflow_controller.m`主控制脚本
**功能**:
```matlab
% 示例结构
function workflow_controller(config_file)
    % 1. 加载配置文件
    config = load_workflow_config(config_file);

    % 2. 自动检测各阶段输入文件
    input_files = detect_input_files(config);

    % 3. 按顺序执行各阶段
    for stage = 1:5
        execute_stage(stage, input_files, config);
        input_files = update_file_paths(stage, config);
    end

    % 4. 生成完整处理报告
    generate_workflow_report(config);
end
```

#### 方案2: 智能文件路径管理
**建议**: 实现自动文件路径解析和复制
**功能**:
- 自动检测上一阶段的输出文件
- 智能复制到下一阶段的输入文件夹
- 支持符号链接减少存储占用
- 自动备份重要文件

#### 方案3: 统一配置文件系统
**建议**: 创建`workflow_config.json`配置文件
**内容**:
```json
{
  "global_settings": {
    "sampling_rate": 2570,
    "segment_length": 60,
    "backup_enabled": true
  },
  "stage_settings": {
    "training": {
      "algorithm_params": {...},
      "output_format": "mat"
    },
    "labeling": {
      "gui_settings": {...},
      "auto_rename": true
    }
  },
  "file_paths": {
    "base_dir": "./",
    "auto_copy": true,
    "cleanup_temp": true
  }
}
```

### 🔧 具体实现建议

#### 1. 创建文件管理工具
**文件**: `file_manager.m`
**功能**:
- `copy_stage_outputs(from_stage, to_stage)` - 自动复制文件
- `validate_file_integrity(file_list)` - 验证文件完整性
- `cleanup_temp_files(stage)` - 清理临时文件
- `generate_file_report(stage)` - 生成文件处理报告

#### 2. 改进现有脚本
**修改建议**:
- 在每个主要脚本开头添加自动文件检测功能
- 在脚本结尾添加输出文件自动复制功能
- 统一错误处理和日志记录机制
- 添加进度显示和用户交互提示

#### 3. 建立质量控制检查点
**检查点位置**:
- 阶段1结束: 验证预处理质量
- 阶段2结束: 检查标注一致性
- 阶段3结束: 验证数据提取完整性
- 阶段4结束: 确认标注优化效果
- 阶段5结束: 最终数据集质量检查

### 📊 性能优化建议

#### 1. 并行处理支持
**实现方式**:
```matlab
% 使用parfor进行批量处理
parfor i = 1:length(file_list)
    process_single_file(file_list{i}, config);
end
```

#### 2. 内存优化
**策略**:
- 分批处理大文件
- 及时清理不需要的变量
- 使用内存映射文件处理大数据集
- 实现数据流式处理

#### 3. 缓存机制
**功能**:
- 缓存频繁使用的计算结果
- 支持断点续传功能
- 智能检测文件变化，避免重复处理

### 🎯 实施优先级

#### 高优先级（立即实施）
1. **创建主控制脚本** - 统一工作流程入口
2. **实现自动文件复制** - 减少手动操作
3. **统一配置管理** - 提高参数一致性

#### 中优先级（短期实施）
1. **添加质量控制检查点** - 提高数据质量
2. **改进错误处理机制** - 增强系统稳定性
3. **实现进度显示功能** - 改善用户体验

#### 低优先级（长期优化）
1. **并行处理支持** - 提升处理速度
2. **缓存机制实现** - 优化重复操作
3. **Web界面开发** - 提供更友好的操作界面

### 💡 创新功能建议

#### 1. 智能标注建议系统
**功能**: 基于历史标注数据，为新文件提供标注建议
**实现**: 使用机器学习算法分析标注模式

#### 2. 标注质量评分系统
**功能**: 自动评估标注质量，识别可能的错误
**指标**: 一致性、完整性、准确性评分

#### 3. 数据集版本管理
**功能**: 类似Git的版本控制，追踪数据集变化
**特性**: 支持回滚、分支、合并操作

### 📈 预期效果

#### 效率提升
- **减少手动操作**: 80%的文件操作自动化
- **处理速度**: 批量处理速度提升50%
- **错误率降低**: 自动化检查减少90%的人为错误

#### 质量改善
- **数据一致性**: 统一的处理标准和参数
- **可追溯性**: 完整的处理历史记录
- **可重现性**: 标准化的处理流程

#### 用户体验
- **操作简化**: 一键式工作流程执行
- **实时反馈**: 处理进度和状态显示
- **智能提示**: 自动检测和修复常见问题

---

## 🤖 自动化工具 (新增)

### 工具概述
为了优化工作流程，在 `5、workflow` 文件夹中新增了三个自动化工具，可以将手动操作减少80%以上：

#### 1. workflow_controller.m - 主控制器
**功能**: 一键式执行整个5阶段工作流程
**特点**:
- 支持从任意阶段开始执行或跳过特定阶段
- 完整的进度显示和状态反馈
- 错误处理和异常恢复机制
- 自动生成处理日志和报告

**使用示例**:
```matlab
% 切换到自动化工具目录
cd('5、workflow')

% 执行完整工作流程
workflow_controller();

% 从阶段2开始执行
workflow_controller('start_stage', 2);

% 跳过阶段1执行其他阶段
workflow_controller('skip_stages', [1]);

% 预演模式（不实际执行）
workflow_controller('dry_run', true);

% 交互式模式
workflow_controller('interactive', true);
```

#### 2. file_manager.m - 文件管理器
**功能**: 自动管理各阶段间的文件传递
**特点**:
- 自动检测上一阶段的输出文件
- 智能复制到下一阶段的输入文件夹
- 文件完整性验证和重复文件处理
- 自动备份和临时文件清理

**使用示例**:
```matlab
% 切换到自动化工具目录
cd('5、workflow')

% 复制阶段0输出到阶段1输入
file_manager('copy_stage_outputs', 0, 1);

% 验证文件完整性
result = file_manager('validate_files', file_list);

% 清理临时文件
file_manager('cleanup_temp', 2);

% 生成文件报告
report = file_manager('generate_report', 3);
```

#### 3. config_manager.m - 配置管理器
**功能**: 统一管理所有阶段的处理参数
**特点**:
- JSON格式的配置文件，包含全局设置和各阶段专用参数
- 配置文件的版本管理和备份
- 配置参数的合理性检查
- 运行时动态修改部分参数

**使用示例**:
```matlab
% 切换到自动化工具目录
cd('5、workflow')

% 加载配置
config = config_manager('load');

% 获取采样率
fs = config_manager('get', 'global_settings.sampling_rate');

% 设置并行处理
config_manager('set', 'global_settings.parallel_processing', true);

% 备份配置
config_manager('backup');

% 验证配置
config_manager('validate');
```

### 快速开始
```matlab
% 1. 切换到自动化工具目录
cd('5、workflow')

% 2. 验证安装
verify_installation()

% 3. 查看使用示例
workflow_demo()

% 4. 预演模式熟悉流程
workflow_controller('dry_run', true)

% 5. 执行完整工作流程
workflow_controller()
```

### 配置文件
- `5、workflow/workflow_config.json`: 主配置文件，包含所有参数设置
- 支持自定义配置文件和参数动态修改
- 自动备份和版本管理功能

### 兼容性保证
- **完全兼容**: 不修改任何现有脚本文件
- **增强层设计**: 作为现有流程的增强，而非替代
- **独立运行**: 现有工作流程仍可独立使用
- **渐进式采用**: 可以选择性使用部分自动化功能

### 详细文档
- `5、workflow/README.md`: 自动化工具专用说明
- `5、workflow/AUTOMATION_GUIDE.md`: 详细使用指南
- `5、workflow/workflow_demo.m`: 完整使用示例
- `5、workflow/test_automation_tools.m`: 功能测试脚本

---

*最后更新: 2024年8月*
*版本: v1.1 (新增自动化工具)*
