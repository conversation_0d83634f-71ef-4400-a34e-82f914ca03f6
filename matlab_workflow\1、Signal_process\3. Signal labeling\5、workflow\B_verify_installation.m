function verify_installation()
%VERIFY_INSTALLATION 验证自动化工具安装和配置
%   检查所有必需的文件是否存在，配置是否正确，
%   并验证基本功能是否正常工作。
%
%   作者: Augment Agent
%   日期: 2024-08-22
%   版本: 1.0

    fprintf('=== 肠鸣音信号标注工作流程自动化工具安装验证 ===\n\n');
    
    % 验证结果统计
    totalChecks = 0;
    passedChecks = 0;
    warnings = 0;
    
    %% 1. 检查核心文件
    fprintf('1. 检查核心文件\n');
    fprintf('===============\n');
    
    coreFiles = {
        'workflow_controller.m', '主控制器';
        'file_manager.m', '文件管理器';
        'config_manager.m', '配置管理器';
        'workflow_config.json', '配置文件';
        'AUTOMATION_GUIDE.md', '使用指南';
        'workflow_demo.m', '使用示例';
        'test_automation_tools.m', '功能测试'
    };
    
    for i = 1:size(coreFiles, 1)
        totalChecks = totalChecks + 1;
        fileName = coreFiles{i, 1};
        description = coreFiles{i, 2};
        
        if exist(fileName, 'file')
            fprintf('✓ %s (%s)\n', fileName, description);
            passedChecks = passedChecks + 1;
        else
            fprintf('✗ %s (%s) - 文件缺失\n', fileName, description);
        end
    end
    
    fprintf('\n');
    
    %% 2. 检查阶段目录结构
    fprintf('2. 检查阶段目录结构\n');
    fprintf('==================\n');
    
    expectedDirs = {
        '0、训练标记';
        '1、信号标注器APP标注';
        '2、标注得到数据集';
        '3、标注结果返回标注器_继续或修改标注';
        '4、整理数据集'
    };
    
    for i = 1:length(expectedDirs)
        totalChecks = totalChecks + 1;
        dirName = expectedDirs{i};
        
        if exist(dirName, 'dir')
            fprintf('✓ 阶段%d目录: %s\n', i-1, dirName);
            passedChecks = passedChecks + 1;
            
            % 检查子目录
            subDirs = {'1、Raw data', '2、Processed data', '3、Backup'};
            for j = 1:length(subDirs)
                subDirPath = fullfile(dirName, subDirs{j});
                if exist(subDirPath, 'dir')
                    fprintf('  ✓ %s\n', subDirs{j});
                else
                    fprintf('  ⚠ %s - 子目录缺失\n', subDirs{j});
                    warnings = warnings + 1;
                end
            end
        else
            fprintf('✗ 阶段%d目录: %s - 目录缺失\n', i-1, dirName);
        end
    end
    
    fprintf('\n');
    
    %% 3. 验证配置文件
    fprintf('3. 验证配置文件\n');
    fprintf('==============\n');
    
    try
        totalChecks = totalChecks + 1;
        
        % 检查配置文件存在性
        if ~exist('workflow_config.json', 'file')
            fprintf('✗ 配置文件不存在\n');
        else
            fprintf('✓ 配置文件存在\n');
            
            % 验证JSON格式
            try
                config = config_manager('load');
                fprintf('✓ 配置文件格式正确\n');
                
                % 验证必需字段
                requiredFields = {'workflow_info', 'global_settings', 'stage_settings', 'file_paths'};
                allFieldsPresent = true;
                
                for i = 1:length(requiredFields)
                    if isfield(config, requiredFields{i})
                        fprintf('  ✓ %s\n', requiredFields{i});
                    else
                        fprintf('  ✗ %s - 字段缺失\n', requiredFields{i});
                        allFieldsPresent = false;
                    end
                end
                
                if allFieldsPresent
                    fprintf('✓ 配置文件结构完整\n');
                    passedChecks = passedChecks + 1;
                else
                    fprintf('✗ 配置文件结构不完整\n');
                end
                
            catch ME
                fprintf('✗ 配置文件格式错误: %s\n', ME.message);
            end
        end
        
    catch ME
        fprintf('✗ 配置文件验证失败: %s\n', ME.message);
    end
    
    fprintf('\n');
    
    %% 4. 测试基本功能
    fprintf('4. 测试基本功能\n');
    fprintf('==============\n');
    
    % 测试配置管理器
    try
        totalChecks = totalChecks + 1;
        config = config_manager('load');
        fs = config_manager('get', 'global_settings.sampling_rate');
        assert(isnumeric(fs) && fs > 0, '采样率应该是正数');
        fprintf('✓ 配置管理器功能正常\n');
        passedChecks = passedChecks + 1;
    catch ME
        fprintf('✗ 配置管理器功能异常: %s\n', ME.message);
    end
    
    % 测试文件管理器
    try
        totalChecks = totalChecks + 1;
        outputFiles = file_manager('detect_outputs', 0);
        assert(iscell(outputFiles), '输出文件列表应该是cell数组');
        fprintf('✓ 文件管理器功能正常\n');
        passedChecks = passedChecks + 1;
    catch ME
        fprintf('✗ 文件管理器功能异常: %s\n', ME.message);
    end
    
    % 测试工作流程控制器（预演模式）
    try
        totalChecks = totalChecks + 1;
        workflow_controller('dry_run', true, 'start_stage', 0, 'end_stage', 0);
        fprintf('✓ 工作流程控制器功能正常\n');
        passedChecks = passedChecks + 1;
    catch ME
        fprintf('✗ 工作流程控制器功能异常: %s\n', ME.message);
    end
    
    fprintf('\n');
    
    %% 5. 检查MATLAB版本兼容性
    fprintf('5. 检查MATLAB版本兼容性\n');
    fprintf('======================\n');
    
    totalChecks = totalChecks + 1;
    matlabVersion = version('-release');
    matlabYear = str2double(matlabVersion(1:4));
    
    fprintf('当前MATLAB版本: %s\n', matlabVersion);
    
    if matlabYear >= 2018
        fprintf('✓ MATLAB版本兼容 (推荐2018b或更高版本)\n');
        passedChecks = passedChecks + 1;
    else
        fprintf('⚠ MATLAB版本较旧，可能存在兼容性问题\n');
        warnings = warnings + 1;
    end
    
    % 检查必需的工具箱
    requiredToolboxes = {
        'Signal Processing Toolbox', 'signal';
        'Statistics and Machine Learning Toolbox', 'stats'
    };
    
    for i = 1:size(requiredToolboxes, 1)
        toolboxName = requiredToolboxes{i, 1};
        toolboxId = requiredToolboxes{i, 2};
        
        if license('test', toolboxId)
            fprintf('✓ %s 可用\n', toolboxName);
        else
            fprintf('⚠ %s 不可用 - 某些功能可能受限\n', toolboxName);
            warnings = warnings + 1;
        end
    end
    
    fprintf('\n');
    
    %% 6. 检查磁盘空间
    fprintf('6. 检查磁盘空间\n');
    fprintf('==============\n');
    
    try
        totalChecks = totalChecks + 1;
        
        % 获取当前目录信息
        currentDir = pwd;
        if ispc
            % Windows系统
            [status, result] = system(['dir "' currentDir '" /-c']);
            if status == 0
                fprintf('✓ 磁盘空间检查完成\n');
                passedChecks = passedChecks + 1;
            else
                fprintf('⚠ 无法检查磁盘空间\n');
                warnings = warnings + 1;
            end
        else
            % Unix/Linux/Mac系统
            [status, result] = system(['df -h "' currentDir '"']);
            if status == 0
                fprintf('✓ 磁盘空间检查完成\n');
                passedChecks = passedChecks + 1;
            else
                fprintf('⚠ 无法检查磁盘空间\n');
                warnings = warnings + 1;
            end
        end
        
    catch ME
        fprintf('⚠ 磁盘空间检查失败: %s\n', ME.message);
        warnings = warnings + 1;
    end
    
    fprintf('\n');
    
    %% 验证结果汇总
    fprintf('=== 验证结果汇总 ===\n');
    fprintf('总检查项: %d\n', totalChecks);
    fprintf('通过检查: %d\n', passedChecks);
    fprintf('失败检查: %d\n', totalChecks - passedChecks);
    fprintf('警告信息: %d\n', warnings);
    
    successRate = passedChecks / totalChecks * 100;
    fprintf('成功率: %.1f%%\n', successRate);
    
    fprintf('\n');
    
    %% 给出建议
    if successRate >= 90
        fprintf('🎉 安装验证通过！\n');
        fprintf('✅ 自动化工具已正确安装并可以正常使用。\n\n');
        
        fprintf('建议的下一步操作:\n');
        fprintf('1. 运行 workflow_demo.m 查看详细使用示例\n');
        fprintf('2. 阅读 AUTOMATION_GUIDE.md 了解完整功能\n');
        fprintf('3. 使用预演模式熟悉工作流程: workflow_controller(''dry_run'', true)\n');
        fprintf('4. 开始使用完整工作流程: workflow_controller()\n');
        
    elseif successRate >= 70
        fprintf('⚠️ 安装基本完成，但存在一些问题。\n');
        fprintf('🔧 建议修复上述问题后再使用自动化工具。\n\n');
        
        fprintf('常见问题解决方案:\n');
        fprintf('1. 缺失文件: 重新下载或复制相关文件\n');
        fprintf('2. 目录结构问题: 手动创建缺失的子目录\n');
        fprintf('3. 配置文件问题: 运行 config_manager(''reset'') 重置配置\n');
        fprintf('4. 权限问题: 确保MATLAB有足够的文件系统访问权限\n');
        
    else
        fprintf('❌ 安装验证失败！\n');
        fprintf('🚨 存在严重问题，请检查安装过程。\n\n');
        
        fprintf('建议的修复步骤:\n');
        fprintf('1. 检查所有文件是否正确复制到目标目录\n');
        fprintf('2. 验证目录结构是否完整\n');
        fprintf('3. 确认MATLAB版本和工具箱要求\n');
        fprintf('4. 检查文件系统权限设置\n');
        fprintf('5. 如问题持续，请联系技术支持\n');
    end
    
    fprintf('\n验证完成时间: %s\n', datestr(now));
    fprintf('=== 验证结束 ===\n');
end
