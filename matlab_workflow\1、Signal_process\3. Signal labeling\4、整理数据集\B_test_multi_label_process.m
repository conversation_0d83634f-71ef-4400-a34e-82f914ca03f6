%% 测试修改后的multi_label_process脚本
%   验证修改后的批量处理脚本是否能正确处理当前文件夹结构的数据
%
%   测试内容：
%   1. 检查文件夹结构和文件存在性
%   2. 验证标注文件格式
%   3. 测试信号名称解析功能
%   4. 验证原始数据文件访问
%   5. 测试单个标注的提取过程
%
%   Author: 肠鸣音信号分析团队
%   Date: 2025年

clear;
clc;
close all;

fprintf('=== 测试修改后的multi_label_process脚本 ===\n\n');

%% 1. 检查文件夹结构
fprintf('1. 检查文件夹结构...\n');

% 检查必要的文件夹
requiredDirs = {'1、Raw data', '4、Label'};
for i = 1:length(requiredDirs)
    dirName = requiredDirs{i};
    if exist(dirName, 'dir')
        fprintf('  ✓ %s 文件夹存在\n', dirName);
    else
        fprintf('  ❌ %s 文件夹不存在\n', dirName);
    end
end

% 检查输出文件夹（如果不存在会自动创建）
outputDir = '2、Processed data';
if exist(outputDir, 'dir')
    fprintf('  ✓ %s 文件夹存在\n', outputDir);
else
    fprintf('  ⚠ %s 文件夹不存在（将自动创建）\n', outputDir);
end

%% 2. 检查标注文件
fprintf('\n2. 检查标注文件...\n');

labelFile = fullfile('4、Label', 'ls_data3.mat');
if exist(labelFile, 'file')
    fprintf('  ✓ 标注文件存在: %s\n', labelFile);
    
    try
        data = load(labelFile);
        fprintf('  ✓ 标注文件加载成功\n');
        
        if isfield(data, 'ls')
            fprintf('  ✓ 包含ls变量\n');
            ls = data.ls;
            
            if isa(ls, 'labeledSignalSet')
                fprintf('  ✓ ls是labeledSignalSet对象\n');
                fprintf('    成员数量: %d\n', ls.NumMembers);
                fprintf('    标签表行数: %d\n', height(ls.Labels));
                
                % 显示前几个信号名称
                fprintf('    前3个信号名称:\n');
                for i = 1:min(3, height(ls.Labels))
                    signalName = ls.Labels.Row{i};
                    bsTable = ls.Labels{i, 'BS'}{1};
                    labelCount = height(bsTable);
                    fprintf('      %d. %s (%d个标注)\n', i, signalName, labelCount);
                end
                
            else
                fprintf('  ❌ ls不是labeledSignalSet对象，而是: %s\n', class(ls));
            end
        else
            fprintf('  ❌ 不包含ls变量\n');
        end
        
    catch ME
        fprintf('  ❌ 标注文件加载失败: %s\n', ME.message);
    end
else
    fprintf('  ❌ 标注文件不存在: %s\n', labelFile);
end

%% 3. 测试信号名称解析功能
fprintf('\n3. 测试信号名称解析功能...\n');

% 测试用例
testSignalNames = {
    'data3_5min_seg002_tt1',
    'data1_5min_seg001_tt1',
    'data12_5min_seg005_tt1'
};

for i = 1:length(testSignalNames)
    signalName = testSignalNames{i};
    fprintf('  测试信号名称: %s\n', signalName);
    
    try
        [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
        fprintf('    ✓ 解析成功: dataset=%s, segment=%d, channel=%s\n', ...
            datasetInfo, segmentNum, channelInfo);
    catch ME
        fprintf('    ❌ 解析失败: %s\n', ME.message);
    end
end

%% 4. 检查原始数据文件
fprintf('\n4. 检查原始数据文件...\n');

rawDataDir = '1、Raw data';
rawFiles = dir(fullfile(rawDataDir, '*.mat'));
fprintf('  找到 %d 个原始数据文件\n', length(rawFiles));

if length(rawFiles) > 0
    % 检查前几个文件
    checkCount = min(3, length(rawFiles));
    fprintf('  检查前 %d 个文件:\n', checkCount);
    
    for i = 1:checkCount
        fileName = rawFiles(i).name;
        filePath = fullfile(rawDataDir, fileName);
        fprintf('    %d. %s\n', i, fileName);
        
        try
            data = load(filePath);
            varNames = fieldnames(data);
            fprintf('       包含的变量: %s\n', strjoin(varNames, ', '));

            % 检查是否包含时间表格式的变量
            hasTimeTable = false;
            for v = 1:length(varNames)
                varName = varNames{v};
                varData = data.(varName);
                if istimetable(varData)
                    fprintf('       ✓ %s是时间表格式\n', varName);
                    fprintf('       数据大小: %dx%d\n', height(varData), width(varData));
                    hasTimeTable = true;
                end
            end

            if ~hasTimeTable
                fprintf('       ❌ 没有找到时间表格式的变量\n');
            end
        catch ME
            fprintf('       ❌ 文件加载失败: %s\n', ME.message);
        end
    end
else
    fprintf('  ❌ 没有找到原始数据文件\n');
end

%% 5. 测试单个标注的提取过程（如果数据可用）
fprintf('\n5. 测试单个标注的提取过程...\n');

if exist(labelFile, 'file') && length(rawFiles) > 0
    try
        % 加载标注文件
        load(labelFile, 'ls');
        
        % 找到第一个有标注的信号
        targetSignal = '';
        targetBsTable = [];
        
        for i = 1:height(ls.Labels)
            signalName = ls.Labels.Row{i};
            bsTable = ls.Labels{i, 'BS'}{1};
            
            if ~isempty(bsTable) && height(bsTable) > 0
                targetSignal = signalName;
                targetBsTable = bsTable;
                break;
            end
        end
        
        if ~isempty(targetSignal)
            fprintf('  测试信号: %s\n', targetSignal);
            fprintf('  标注数量: %d\n', height(targetBsTable));
            
            % 解析信号名称
            [datasetInfo, segmentNum, channelInfo] = parseSignalName(targetSignal);
            
            % 构造原始数据文件路径
            rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
            rawFilePath = fullfile(rawDataDir, rawFileName);
            
            if exist(rawFilePath, 'file')
                fprintf('  ✓ 找到对应的原始数据文件: %s\n', rawFileName);
                
                % 加载原始数据
                rawData = load(rawFilePath);
                if isfield(rawData, targetSignal)
                    signalData = rawData.(targetSignal);
                    
                    % 测试第一个标注的提取
                    if height(targetBsTable) > 0
                        roiLimits = targetBsTable.ROILimits(1, :);
                        labelValue = targetBsTable.Value(1);
                        if iscell(labelValue)
                            labelValue = labelValue{1};
                        end
                        
                        fprintf('  测试提取第一个标注:\n');
                        fprintf('    时间范围: %.3f-%.3f秒\n', roiLimits(1), roiLimits(2));
                        fprintf('    标签值: %s\n', labelValue);
                        
                        try
                            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
                            fprintf('    ✓ 信号提取成功\n');
                            fprintf('    提取的数据大小: %dx%d\n', height(extractedSignal), width(extractedSignal));
                            
                            % 计算还原时间
                            segmentOffset = (segmentNum - 1) * 60;
                            realStartTime = roiLimits(1) + segmentOffset;
                            realEndTime = roiLimits(2) + segmentOffset;
                            fprintf('    还原时间范围: %.3f-%.3f秒\n', realStartTime, realEndTime);
                            
                        catch ME
                            fprintf('    ❌ 信号提取失败: %s\n', ME.message);
                        end
                    end
                else
                    fprintf('  ❌ 原始数据文件不包含变量 %s\n', targetSignal);
                    fprintf('    可用变量: %s\n', strjoin(fieldnames(rawData), ', '));
                end
            else
                fprintf('  ❌ 找不到对应的原始数据文件: %s\n', rawFilePath);
            end
        else
            fprintf('  ❌ 没有找到包含标注的信号\n');
        end
        
    catch ME
        fprintf('  ❌ 测试过程出错: %s\n', ME.message);
    end
else
    fprintf('  ⚠ 跳过测试（缺少必要的数据文件）\n');
end

%% 总结
fprintf('\n=== 测试完成 ===\n');
fprintf('如果以上测试都通过，可以运行 multi_label_process 进行批量处理\n');

%% 辅助函数：解析信号名称（复制自主脚本）
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
    % 使用正则表达式解析标准文件名格式
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');

    if isempty(tokens)
        error('PARSESIGNALNAME:InvalidFormat', ...
              '无法解析信号名称: %s\n期望格式: data{N}_5min_seg{XXX}_{channel}', signalName);
    end

    % 提取解析结果
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end
