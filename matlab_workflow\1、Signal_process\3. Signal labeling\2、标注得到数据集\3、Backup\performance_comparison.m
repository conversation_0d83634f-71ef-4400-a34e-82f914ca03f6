%% 数据加载方法性能对比测试
%PERFORMANCE_COMPARISON 对比不同数据加载方法的性能
%   本脚本对比两种数据加载方法的性能：
%   1. embedded模式：从labeledSignalSet直接提取
%   2. external模式：从外部文件加载
%
%   测试指标：
%   - 处理时间
%   - 内存使用
%   - 文件I/O次数
%   - 数据一致性验证
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：1.0

clear;
clc;
close all;

fprintf('=== 数据加载方法性能对比测试 ===\n\n');

%% 测试配置
testConfig = struct();
testConfig.labelFile = '4、Label/ls_data3.mat';
testConfig.rawDataDir = '1、Raw data';
testConfig.numIterations = 3; % 重复测试次数以获得平均值

%% 加载测试数据
fprintf('加载测试数据...\n');
load(testConfig.labelFile);
labels = ls.Labels;
sources = ls.Source;

% 找到有标注的信号进行测试
testIndices = [];
for i = 1:height(labels)
    bsTable = labels{i, 'BS'}{1};
    if ~isempty(bsTable) && height(bsTable) > 0
        testIndices(end+1) = i;
    end
end

fprintf('找到 %d 个有标注的信号用于测试\n', length(testIndices));

if isempty(testIndices)
    fprintf('没有找到有标注的信号，无法进行性能测试\n');
    return;
end

%% 测试1：embedded模式（从labeledSignalSet直接提取）
fprintf('\n--- 测试1：embedded模式 ---\n');
embeddedResults = struct();
embeddedResults.times = [];
embeddedResults.memoryUsage = [];
embeddedResults.extractedData = {};

for iter = 1:testConfig.numIterations
    fprintf('迭代 %d/%d...\n', iter, testConfig.numIterations);
    
    memStart = memory;
    tic;
    
    extractedCount = 0;
    for idx = testIndices
        signalName = labels.Row{idx};
        bsTable = labels{idx, 'BS'}{1};
        
        % 从labeledSignalSet直接提取
        signalData = sources{idx};
        
        % 处理每个标注
        for j = 1:height(bsTable)
            roiLimits = bsTable.ROILimits(j, :);
            
            % 提取信号片段
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
            
            if iter == 1 % 只在第一次迭代时保存数据用于一致性检查
                embeddedResults.extractedData{end+1} = extractedSignal;
            end
            
            extractedCount = extractedCount + 1;
        end
    end
    
    embeddedTime = toc;
    memEnd = memory;
    
    embeddedResults.times(iter) = embeddedTime;
    embeddedResults.memoryUsage(iter) = (memEnd.MemUsedMATLAB - memStart.MemUsedMATLAB) / 1024 / 1024;
    
    fprintf('  时间: %.3f秒, 内存增长: %.2f MB, 提取数量: %d\n', ...
        embeddedTime, embeddedResults.memoryUsage(iter), extractedCount);
end

%% 测试2：external模式（从外部文件加载）
fprintf('\n--- 测试2：external模式 ---\n');
externalResults = struct();
externalResults.times = [];
externalResults.memoryUsage = [];
externalResults.extractedData = {};
externalResults.fileLoads = 0;

for iter = 1:testConfig.numIterations
    fprintf('迭代 %d/%d...\n', iter, testConfig.numIterations);
    
    memStart = memory;
    tic;
    
    extractedCount = 0;
    loadedFiles = containers.Map(); % 缓存已加载的文件
    
    for idx = testIndices
        signalName = labels.Row{idx};
        bsTable = labels{idx, 'BS'}{1};
        
        % 解析文件名
        [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
        rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
        rawFilePath = fullfile(testConfig.rawDataDir, rawFileName);
        
        % 从外部文件加载（带缓存优化）
        if ~isKey(loadedFiles, rawFilePath)
            rawData = load(rawFilePath);
            loadedFiles(rawFilePath) = rawData;
            if iter == 1
                externalResults.fileLoads = externalResults.fileLoads + 1;
            end
        else
            rawData = loadedFiles(rawFilePath);
        end
        
        signalData = rawData.(signalName);
        
        % 处理每个标注
        for j = 1:height(bsTable)
            roiLimits = bsTable.ROILimits(j, :);
            
            % 提取信号片段
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);
            
            if iter == 1 % 只在第一次迭代时保存数据用于一致性检查
                externalResults.extractedData{end+1} = extractedSignal;
            end
            
            extractedCount = extractedCount + 1;
        end
    end
    
    externalTime = toc;
    memEnd = memory;
    
    externalResults.times(iter) = externalTime;
    externalResults.memoryUsage(iter) = (memEnd.MemUsedMATLAB - memStart.MemUsedMATLAB) / 1024 / 1024;
    
    fprintf('  时间: %.3f秒, 内存增长: %.2f MB, 提取数量: %d\n', ...
        externalTime, externalResults.memoryUsage(iter), extractedCount);
end

%% 数据一致性验证
fprintf('\n--- 数据一致性验证 ---\n');
consistencyCheck = true;

if length(embeddedResults.extractedData) == length(externalResults.extractedData)
    for i = 1:length(embeddedResults.extractedData)
        embeddedData = embeddedResults.extractedData{i};
        externalData = externalResults.extractedData{i};
        
        % 检查数据值是否相等
        if ~isequal(embeddedData{:,1}, externalData{:,1})
            fprintf('❌ 数据不一致：提取片段 %d\n', i);
            consistencyCheck = false;
        end
        
        % 检查时间是否相等（允许微小差异）
        timeDiff = abs(seconds(embeddedData.Time - externalData.Time));
        if any(timeDiff > 1e-6)
            fprintf('⚠️  时间轻微差异：提取片段 %d (最大差异: %.2e秒)\n', i, max(timeDiff));
        end
    end
    
    if consistencyCheck
        fprintf('✅ 数据一致性验证通过\n');
    end
else
    fprintf('❌ 提取数量不匹配：embedded=%d, external=%d\n', ...
        length(embeddedResults.extractedData), length(externalResults.extractedData));
    consistencyCheck = false;
end

%% 性能统计分析
fprintf('\n=== 性能对比结果 ===\n');

% 计算平均值和标准差
embeddedAvgTime = mean(embeddedResults.times);
embeddedStdTime = std(embeddedResults.times);
embeddedAvgMem = mean(embeddedResults.memoryUsage);

externalAvgTime = mean(externalResults.times);
externalStdTime = std(externalResults.times);
externalAvgMem = mean(externalResults.memoryUsage);

% 性能提升计算
timeImprovement = ((externalAvgTime - embeddedAvgTime) / externalAvgTime) * 100;
memoryImprovement = ((externalAvgMem - embeddedAvgMem) / externalAvgMem) * 100;

fprintf('\n处理时间对比：\n');
fprintf('  Embedded模式: %.3f ± %.3f 秒\n', embeddedAvgTime, embeddedStdTime);
fprintf('  External模式: %.3f ± %.3f 秒\n', externalAvgTime, externalStdTime);
fprintf('  性能提升: %.1f%%\n', timeImprovement);

fprintf('\n内存使用对比：\n');
fprintf('  Embedded模式: %.2f MB\n', embeddedAvgMem);
fprintf('  External模式: %.2f MB\n', externalAvgMem);
fprintf('  内存节省: %.1f%%\n', memoryImprovement);

fprintf('\n文件I/O对比：\n');
fprintf('  Embedded模式: 1 次文件加载 (labeledSignalSet)\n');
fprintf('  External模式: %d 次文件加载 (原始数据文件)\n', externalResults.fileLoads);
fprintf('  I/O减少: %.1f%%\n', ((externalResults.fileLoads - 1) / externalResults.fileLoads) * 100);

%% 可视化对比结果
figure('Name', '性能对比结果', 'Position', [100, 100, 1000, 600]);

subplot(2,2,1);
bar([embeddedAvgTime, externalAvgTime]);
set(gca, 'XTickLabel', {'Embedded', 'External'});
ylabel('处理时间 (秒)');
title('处理时间对比');
grid on;

subplot(2,2,2);
bar([embeddedAvgMem, externalAvgMem]);
set(gca, 'XTickLabel', {'Embedded', 'External'});
ylabel('内存使用 (MB)');
title('内存使用对比');
grid on;

subplot(2,2,3);
bar([1, externalResults.fileLoads]);
set(gca, 'XTickLabel', {'Embedded', 'External'});
ylabel('文件加载次数');
title('文件I/O对比');
grid on;

subplot(2,2,4);
improvements = [timeImprovement, memoryImprovement, ((externalResults.fileLoads - 1) / externalResults.fileLoads) * 100];
bar(improvements);
set(gca, 'XTickLabel', {'时间', '内存', 'I/O'});
ylabel('改善百分比 (%)');
title('Embedded模式的性能改善');
grid on;

%% 保存测试结果
testResults = struct();
testResults.testTime = datetime('now');
testResults.embeddedResults = embeddedResults;
testResults.externalResults = externalResults;
testResults.consistencyCheck = consistencyCheck;
testResults.performance = struct();
testResults.performance.timeImprovement = timeImprovement;
testResults.performance.memoryImprovement = memoryImprovement;
testResults.performance.ioReduction = ((externalResults.fileLoads - 1) / externalResults.fileLoads) * 100;

save('performance_comparison_results.mat', 'testResults');
fprintf('\n测试结果已保存: performance_comparison_results.mat\n');

%% 推荐建议
fprintf('\n=== 推荐建议 ===\n');
if timeImprovement > 10
    fprintf('✅ 推荐使用embedded模式：显著的性能提升 (%.1f%%)\n', timeImprovement);
elseif timeImprovement > 0
    fprintf('✅ 推荐使用embedded模式：轻微的性能提升 (%.1f%%)\n', timeImprovement);
else
    fprintf('⚠️  两种模式性能相近，可根据具体需求选择\n');
end

if consistencyCheck
    fprintf('✅ 数据一致性验证通过，可安全切换到embedded模式\n');
else
    fprintf('❌ 数据一致性验证失败，建议进一步调试后再切换\n');
end

fprintf('\n测试完成！\n');

%% 辅助函数
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');
    
    if isempty(tokens)
        error('无法解析信号名称: %s', signalName);
    end
    
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end
