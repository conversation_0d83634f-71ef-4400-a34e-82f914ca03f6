function varargout = file_manager(action, varargin)
%FILE_MANAGER 工作流程文件管理器
%   自动管理各阶段间的文件传递，减少手动复制操作。
%   支持文件完整性验证、智能复制、符号链接和自动备份功能。
%
%   语法:
%   file_manager('copy_stage_outputs', from_stage, to_stage)
%   result = file_manager('validate_files', file_list)
%   file_manager('cleanup_temp', stage)
%   report = file_manager('generate_report', stage)
%   file_manager('create_symlinks', source_dir, target_dir)
%   file_manager('auto_backup', stage)
%
%   输入参数:
%   action      - 操作类型 (string/char)
%               'copy_stage_outputs': 复制阶段输出文件
%               'validate_files': 验证文件完整性
%               'cleanup_temp': 清理临时文件
%               'generate_report': 生成文件处理报告
%               'create_symlinks': 创建符号链接
%               'auto_backup': 自动备份
%               'detect_outputs': 检测阶段输出文件
%               'prepare_inputs': 准备阶段输入文件
%   varargin    - 根据action类型的额外参数
%
%   输出参数:
%   varargout   - 根据action类型的返回值
%
%   示例:
%   % 复制阶段0的输出到阶段1的输入
%   file_manager('copy_stage_outputs', 0, 1);
%   
%   % 验证文件完整性
%   files = {'file1.mat', 'file2.mat'};
%   result = file_manager('validate_files', files);
%   
%   % 清理阶段2的临时文件
%   file_manager('cleanup_temp', 2);
%
%   作者: Augment Agent
%   日期: 2024-08-22
%   版本: 1.0

    % 根据操作类型执行相应功能
    switch lower(action)
        case 'copy_stage_outputs'
            if nargin < 3
                error('复制阶段输出需要提供from_stage和to_stage参数');
            end
            copyStageOutputs(varargin{1}, varargin{2});
            
        case 'validate_files'
            if nargin < 2
                error('验证文件需要提供file_list参数');
            end
            varargout{1} = validateFiles(varargin{1});
            
        case 'cleanup_temp'
            if nargin < 2
                error('清理临时文件需要提供stage参数');
            end
            cleanupTemp(varargin{1});
            
        case 'generate_report'
            if nargin < 2
                error('生成报告需要提供stage参数');
            end
            varargout{1} = generateReport(varargin{1});
            
        case 'create_symlinks'
            if nargin < 3
                error('创建符号链接需要提供source_dir和target_dir参数');
            end
            createSymlinks(varargin{1}, varargin{2});
            
        case 'auto_backup'
            if nargin < 2
                error('自动备份需要提供stage参数');
            end
            autoBackup(varargin{1});
            
        case 'detect_outputs'
            if nargin < 2
                error('检测输出文件需要提供stage参数');
            end
            varargout{1} = detectOutputs(varargin{1});
            
        case 'prepare_inputs'
            if nargin < 2
                error('准备输入文件需要提供stage参数');
            end
            prepareInputs(varargin{1});
            
        otherwise
            error('未知的操作类型: %s', action);
    end
end

function copyStageOutputs(fromStage, toStage)
%COPYSTAGEOUTPUTS 复制阶段输出文件到下一阶段输入
    try
        % 加载配置
        config = config_manager('load');
        
        % 获取阶段目录
        stageNames = config.file_paths.stage_directories;
        if fromStage < 0 || fromStage >= length(stageNames) || toStage < 0 || toStage >= length(stageNames)
            error('阶段编号无效');
        end
        
        fromDir = stageNames{fromStage + 1};  % MATLAB索引从1开始
        toDir = stageNames{toStage + 1};
        
        fprintf('开始复制文件: %s → %s\n', fromDir, toDir);
        
        % 检测源阶段的输出文件
        outputFiles = detectOutputs(fromStage);
        if isempty(outputFiles)
            warning('阶段%d没有检测到输出文件', fromStage);
            return;
        end
        
        % 确定目标目录
        targetInputDir = fullfile(toDir, config.file_paths.common_subdirs.raw_data);
        if ~exist(targetInputDir, 'dir')
            mkdir(targetInputDir);
            fprintf('创建目标目录: %s\n', targetInputDir);
        end
        
        % 复制文件
        copyCount = 0;
        skipCount = 0;
        
        for i = 1:length(outputFiles)
            sourceFile = outputFiles{i};
            [~, fileName, ext] = fileparts(sourceFile);
            targetFile = fullfile(targetInputDir, [fileName, ext]);
            
            % 检查目标文件是否已存在
            if exist(targetFile, 'file')
                % 比较文件修改时间和大小
                sourceInfo = dir(sourceFile);
                targetInfo = dir(targetFile);
                
                if sourceInfo.datenum > targetInfo.datenum || sourceInfo.bytes ~= targetInfo.bytes
                    copyfile(sourceFile, targetFile);
                    copyCount = copyCount + 1;
                    fprintf('更新文件: %s\n', fileName);
                else
                    skipCount = skipCount + 1;
                    fprintf('跳过文件: %s (已是最新)\n', fileName);
                end
            else
                copyfile(sourceFile, targetFile);
                copyCount = copyCount + 1;
                fprintf('复制文件: %s\n', fileName);
            end
        end
        
        fprintf('✓ 文件复制完成: 复制%d个，跳过%d个\n', copyCount, skipCount);
        
    catch ME
        error('复制阶段输出失败: %s', ME.message);
    end
end

function result = validateFiles(fileList)
%VALIDATEFILES 验证文件完整性
    result = struct();
    result.valid_files = {};
    result.invalid_files = {};
    result.missing_files = {};
    result.total_count = length(fileList);
    
    try
        fprintf('开始验证%d个文件...\n', length(fileList));
        
        for i = 1:length(fileList)
            filePath = fileList{i};
            
            if ~exist(filePath, 'file')
                result.missing_files{end+1} = filePath;
                fprintf('✗ 文件不存在: %s\n', filePath);
                continue;
            end
            
            % 检查文件大小
            fileInfo = dir(filePath);
            if fileInfo.bytes == 0
                result.invalid_files{end+1} = filePath;
                fprintf('✗ 文件为空: %s\n', filePath);
                continue;
            end
            
            % 对于.mat文件，尝试加载验证
            [~, ~, ext] = fileparts(filePath);
            if strcmpi(ext, '.mat')
                try
                    temp = load(filePath);
                    if isempty(fieldnames(temp))
                        result.invalid_files{end+1} = filePath;
                        fprintf('✗ MAT文件无数据: %s\n', filePath);
                        continue;
                    end
                catch
                    result.invalid_files{end+1} = filePath;
                    fprintf('✗ MAT文件损坏: %s\n', filePath);
                    continue;
                end
            end
            
            result.valid_files{end+1} = filePath;
            fprintf('✓ 文件有效: %s\n', filePath);
        end
        
        result.valid_count = length(result.valid_files);
        result.invalid_count = length(result.invalid_files);
        result.missing_count = length(result.missing_files);
        
        fprintf('验证完成: 有效%d个，无效%d个，缺失%d个\n', ...
            result.valid_count, result.invalid_count, result.missing_count);
        
    catch ME
        error('文件验证失败: %s', ME.message);
    end
end

function cleanupTemp(stage)
%CLEANUPTEMP 清理指定阶段的临时文件
    try
        config = config_manager('load');
        stageNames = config.file_paths.stage_directories;
        
        if stage < 0 || stage >= length(stageNames)
            error('阶段编号无效');
        end
        
        stageDir = stageNames{stage + 1};
        fprintf('清理阶段%d的临时文件: %s\n', stage, stageDir);
        
        % 定义临时文件模式
        tempPatterns = {'*.tmp', '*~', '*.temp', '*.bak', '.DS_Store', 'Thumbs.db'};
        
        cleanCount = 0;
        for i = 1:length(tempPatterns)
            pattern = tempPatterns{i};
            tempFiles = dir(fullfile(stageDir, '**', pattern));
            
            for j = 1:length(tempFiles)
                if ~tempFiles(j).isdir
                    tempFile = fullfile(tempFiles(j).folder, tempFiles(j).name);
                    delete(tempFile);
                    cleanCount = cleanCount + 1;
                    fprintf('删除临时文件: %s\n', tempFiles(j).name);
                end
            end
        end
        
        fprintf('✓ 清理完成: 删除%d个临时文件\n', cleanCount);
        
    catch ME
        error('清理临时文件失败: %s', ME.message);
    end
end

function report = generateReport(stage)
%GENERATEREPORT 生成文件处理报告
    try
        config = config_manager('load');
        stageNames = config.file_paths.stage_directories;
        
        if stage < 0 || stage >= length(stageNames)
            error('阶段编号无效');
        end
        
        stageDir = stageNames{stage + 1};
        report = struct();
        report.stage = stage;
        report.stage_name = stageDir;
        report.timestamp = datestr(now);
        
        % 统计各子目录的文件
        subdirs = fieldnames(config.file_paths.common_subdirs);
        for i = 1:length(subdirs)
            subdirName = config.file_paths.common_subdirs.(subdirs{i});
            subdirPath = fullfile(stageDir, subdirName);
            
            if exist(subdirPath, 'dir')
                files = dir(fullfile(subdirPath, '*.mat'));
                report.(subdirs{i}) = struct();
                report.(subdirs{i}).count = length(files);
                report.(subdirs{i}).files = {files.name};
                report.(subdirs{i}).total_size = sum([files.bytes]);
            else
                report.(subdirs{i}) = struct('count', 0, 'files', {{}}, 'total_size', 0);
            end
        end
        
        % 保存报告
        reportFile = fullfile(stageDir, sprintf('file_report_stage_%d.mat', stage));
        save(reportFile, 'report');
        
        fprintf('✓ 文件报告生成完成: %s\n', reportFile);
        
    catch ME
        error('生成文件报告失败: %s', ME.message);
    end
end

function outputFiles = detectOutputs(stage)
%DETECTOUTPUTS 检测阶段输出文件
    try
        config = config_manager('load');
        stageNames = config.file_paths.stage_directories;
        
        if stage < 0 || stage >= length(stageNames)
            error('阶段编号无效');
        end
        
        stageDir = stageNames{stage + 1};
        outputDir = fullfile(stageDir, config.file_paths.common_subdirs.processed_data);
        
        outputFiles = {};
        if exist(outputDir, 'dir')
            files = dir(fullfile(outputDir, '*.mat'));
            for i = 1:length(files)
                outputFiles{end+1} = fullfile(files(i).folder, files(i).name);
            end
        end
        
        fprintf('阶段%d检测到%d个输出文件\n', stage, length(outputFiles));
        
    catch ME
        error('检测输出文件失败: %s', ME.message);
    end
end

function prepareInputs(stage)
%PREPAREINPUTS 准备阶段输入文件
    try
        if stage > 0
            % 从上一阶段复制输出文件
            file_manager('copy_stage_outputs', stage-1, stage);
        end
        
        % 验证输入文件
        config = config_manager('load');
        stageNames = config.file_paths.stage_directories;
        stageDir = stageNames{stage + 1};
        inputDir = fullfile(stageDir, config.file_paths.common_subdirs.raw_data);
        
        if exist(inputDir, 'dir')
            files = dir(fullfile(inputDir, '*.mat'));
            fileList = {};
            for i = 1:length(files)
                fileList{end+1} = fullfile(files(i).folder, files(i).name);
            end
            
            if ~isempty(fileList)
                result = validateFiles(fileList);
                if result.invalid_count > 0 || result.missing_count > 0
                    warning('阶段%d输入文件验证发现问题', stage);
                end
            end
        end
        
        fprintf('✓ 阶段%d输入文件准备完成\n', stage);
        
    catch ME
        error('准备输入文件失败: %s', ME.message);
    end
end

function autoBackup(stage)
%AUTOBACKUP 自动备份阶段文件
    try
        config = config_manager('load');
        
        if ~config.global_settings.backup_enabled
            fprintf('备份功能已禁用，跳过备份\n');
            return;
        end
        
        stageNames = config.file_paths.stage_directories;
        if stage < 0 || stage >= length(stageNames)
            error('阶段编号无效');
        end
        
        stageDir = stageNames{stage + 1};
        backupDir = fullfile(stageDir, config.file_paths.common_subdirs.backup);
        
        if ~exist(backupDir, 'dir')
            mkdir(backupDir);
        end
        
        % 备份处理后的数据
        processedDir = fullfile(stageDir, config.file_paths.common_subdirs.processed_data);
        if exist(processedDir, 'dir')
            timestamp = datestr(now, 'yyyymmdd_HHMMSS');
            backupSubdir = fullfile(backupDir, sprintf('backup_%s', timestamp));
            mkdir(backupSubdir);
            
            copyfile(processedDir, backupSubdir);
            fprintf('✓ 阶段%d数据备份完成: %s\n', stage, backupSubdir);
        end
        
    catch ME
        error('自动备份失败: %s', ME.message);
    end
end
