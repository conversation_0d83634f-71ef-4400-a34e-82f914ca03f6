function workflow_controller(varargin)
%WORKFLOW_CONTROLLER 肠鸣音信号标注工作流程主控制器
%   提供一键式执行整个5阶段工作流程的入口，支持从任意阶段开始执行或跳过特定阶段。
%   包含完整的进度显示、错误处理和异常恢复机制。
%
%   语法:
%   workflow_controller()                           % 执行完整工作流程
%   workflow_controller('start_stage', 2)           % 从阶段2开始执行
%   workflow_controller('skip_stages', [1, 3])      % 跳过阶段1和3
%   workflow_controller('config_file', 'custom.json') % 使用自定义配置
%   workflow_controller('interactive', true)        % 交互式模式
%   workflow_controller('dry_run', true)           % 预演模式（不执行）
%
%   输入参数:
%   'start_stage'   - 起始阶段编号 (0-4, 默认: 0)
%   'end_stage'     - 结束阶段编号 (0-4, 默认: 4)
%   'skip_stages'   - 跳过的阶段编号数组 (默认: [])
%   'config_file'   - 配置文件路径 (默认: 'workflow_config.json')
%   'interactive'   - 是否启用交互式模式 (默认: false)
%   'dry_run'       - 是否为预演模式 (默认: false)
%   'auto_backup'   - 是否自动备份 (默认: true)
%   'continue_on_error' - 遇到错误是否继续 (默认: false)
%
%   工作流程阶段:
%   阶段0: 算法训练与开发 (0、训练标记)
%   阶段1: 信号标注器APP标注 (1、信号标注器APP标注)
%   阶段2: 标注得到数据集 (2、标注得到数据集)
%   阶段3: 标注结果返回标注器 (3、标注结果返回标注器_继续或修改标注)
%   阶段4: 整理数据集 (4、整理数据集)
%
%   示例:
%   % 执行完整工作流程
%   workflow_controller();
%   
%   % 从阶段2开始执行到阶段4
%   workflow_controller('start_stage', 2, 'end_stage', 4);
%   
%   % 跳过阶段1，执行其他阶段
%   workflow_controller('skip_stages', [1]);
%   
%   % 交互式模式执行
%   workflow_controller('interactive', true);
%
%   作者: Augment Agent
%   日期: 2024-08-22
%   版本: 1.0

    % 解析输入参数
    p = inputParser;
    addParameter(p, 'start_stage', 0, @(x) isnumeric(x) && x >= 0 && x <= 4);
    addParameter(p, 'end_stage', 4, @(x) isnumeric(x) && x >= 0 && x <= 4);
    addParameter(p, 'skip_stages', [], @(x) isnumeric(x) && all(x >= 0 & x <= 4));
    addParameter(p, 'config_file', 'workflow_config.json', @ischar);
    addParameter(p, 'interactive', false, @islogical);
    addParameter(p, 'dry_run', false, @islogical);
    addParameter(p, 'auto_backup', true, @islogical);
    addParameter(p, 'continue_on_error', false, @islogical);
    
    parse(p, varargin{:});
    params = p.Results;
    
    % 验证参数
    if params.start_stage > params.end_stage
        error('起始阶段不能大于结束阶段');
    end
    
    try
        % 初始化工作流程
        fprintf('\n=== 肠鸣音信号标注工作流程控制器 ===\n');
        fprintf('版本: 1.0\n');
        fprintf('时间: %s\n\n', datestr(now));
        
        % 加载配置
        % 添加5、workflow目录到路径
        workflowDir = fileparts(mfilename('fullpath'));
        addpath(workflowDir);

        if exist(params.config_file, 'file')
            fprintf('加载配置文件: %s\n', params.config_file);
        else
            fprintf('配置文件不存在，使用默认配置\n');
        end
        config = config_manager('load');
        
        % 显示工作流程信息
        displayWorkflowInfo(config, params);
        
        % 交互式确认
        if params.interactive && ~params.dry_run
            if ~confirmExecution(params)
                fprintf('用户取消执行\n');
                return;
            end
        end
        
        % 创建执行日志
        logFile = createLogFile();
        
        % 执行工作流程
        executeWorkflow(config, params, logFile);
        
        % 生成最终报告
        generateFinalReport(config, params, logFile);
        
        fprintf('\n✓ 工作流程执行完成！\n');
        
    catch ME
        fprintf('\n✗ 工作流程执行失败: %s\n', ME.message);
        if params.continue_on_error
            fprintf('继续执行模式已启用，尝试恢复...\n');
            % 这里可以添加错误恢复逻辑
        else
            rethrow(ME);
        end
    end
end

function displayWorkflowInfo(config, params)
%DISPLAYWORKFLOWINFO 显示工作流程信息
    fprintf('工作流程配置:\n');
    fprintf('  起始阶段: %d\n', params.start_stage);
    fprintf('  结束阶段: %d\n', params.end_stage);
    if ~isempty(params.skip_stages)
        fprintf('  跳过阶段: %s\n', mat2str(params.skip_stages));
    end
    fprintf('  交互模式: %s\n', yesno(params.interactive));
    fprintf('  预演模式: %s\n', yesno(params.dry_run));
    fprintf('  自动备份: %s\n', yesno(params.auto_backup));
    fprintf('  错误继续: %s\n', yesno(params.continue_on_error));
    
    % 显示阶段信息
    fprintf('\n执行阶段:\n');
    stageNames = config.file_paths.stage_directories;
    for i = params.start_stage:params.end_stage
        if ~ismember(i, params.skip_stages)
            fprintf('  阶段%d: %s\n', i, stageNames{i+1});
        end
    end
    fprintf('\n');
end

function result = confirmExecution(params)
%CONFIRMEXECUTION 交互式确认执行
    fprintf('是否继续执行工作流程？\n');
    fprintf('输入 y/yes 继续，其他任意键取消: ');
    
    response = input('', 's');
    result = strcmpi(response, 'y') || strcmpi(response, 'yes');
end

function logFile = createLogFile()
%CREATELOGFILE 创建执行日志文件
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    logFile = sprintf('workflow_log_%s.txt', timestamp);
    
    fid = fopen(logFile, 'w');
    if fid ~= -1
        fprintf(fid, '=== 肠鸣音信号标注工作流程执行日志 ===\n');
        fprintf(fid, '开始时间: %s\n\n', datestr(now));
        fclose(fid);
        fprintf('创建日志文件: %s\n', logFile);
    else
        warning('无法创建日志文件');
        logFile = '';
    end
end

function executeWorkflow(config, params, logFile)
%EXECUTEWORKFLOW 执行工作流程
    stageNames = config.file_paths.stage_directories;
    totalStages = params.end_stage - params.start_stage + 1 - length(params.skip_stages);
    currentStage = 0;
    
    for stage = params.start_stage:params.end_stage
        if ismember(stage, params.skip_stages)
            fprintf('跳过阶段%d: %s\n', stage, stageNames{stage+1});
            logMessage(logFile, sprintf('跳过阶段%d: %s', stage, stageNames{stage+1}));
            continue;
        end
        
        currentStage = currentStage + 1;
        
        fprintf('\n--- 执行阶段%d/%d: %s ---\n', currentStage, totalStages, stageNames{stage+1});
        logMessage(logFile, sprintf('开始执行阶段%d: %s', stage, stageNames{stage+1}));
        
        try
            % 显示进度
            showProgress(currentStage, totalStages, sprintf('阶段%d', stage));
            
            % 预演模式
            if params.dry_run
                fprintf('[预演模式] 模拟执行阶段%d\n', stage);
                pause(1); % 模拟执行时间
                continue;
            end
            
            % 准备阶段输入文件
            if stage > params.start_stage
                fprintf('准备输入文件...\n');
                file_manager('prepare_inputs', stage);
            end
            
            % 执行阶段脚本
            executeStageScript(stage, config);
            
            % 自动备份
            if params.auto_backup
                fprintf('执行自动备份...\n');
                file_manager('auto_backup', stage);
            end
            
            % 质量检查
            performQualityCheck(stage, config);
            
            fprintf('✓ 阶段%d执行完成\n', stage);
            logMessage(logFile, sprintf('阶段%d执行完成', stage));
            
        catch ME
            fprintf('✗ 阶段%d执行失败: %s\n', stage, ME.message);
            logMessage(logFile, sprintf('阶段%d执行失败: %s', stage, ME.message));
            
            if ~params.continue_on_error
                rethrow(ME);
            else
                fprintf('继续执行下一阶段...\n');
            end
        end
    end
end

function executeStageScript(stage, config)
%EXECUTESTAGESCRIPT 执行阶段脚本
    stageNames = config.file_paths.stage_directories;
    stageDir = stageNames{stage + 1};

    % 切换到阶段目录（从5、workflow目录出发）
    originalDir = pwd;
    workflowDir = fileparts(mfilename('fullpath'));
    targetDir = fullfile(workflowDir, stageDir);
    cd(targetDir);
    
    try
        switch stage
            case 0
                % 阶段0: 训练标记
                fprintf('执行音频预处理...\n');
                if exist('audio_preprocessing.m', 'file')
                    audio_preprocessing();
                end
                
                fprintf('执行标注算法测试...\n');
                if exist('test_preprocessing.m', 'file')
                    test_preprocessing();
                end
                
            case 1
                % 阶段1: 信号标注器APP标注
                fprintf('启动肠鸣音标注器GUI...\n');
                fprintf('注意: 这是一个交互式GUI应用，需要用户手动操作\n');
                fprintf('请完成标注后按任意键继续...\n');
                
                if exist('BowelSoundLabeler.m', 'file')
                    % 在交互模式下启动GUI
                    BowelSoundLabeler();
                    pause; % 等待用户完成操作
                end
                
            case 2
                % 阶段2: 标注得到数据集
                fprintf('执行标注数据处理...\n');
                if exist('label_process_2.m', 'file')
                    run('label_process_2.m');
                end
                
                fprintf('验证提取结果...\n');
                if exist('validate_extraction_3.m', 'file')
                    run('validate_extraction_3.m');
                end
                
            case 3
                % 阶段3: 标注结果返回标注器
                fprintf('检查标注文件...\n');
                if exist('check_label_file.m', 'file')
                    run('check_label_file.m');
                end
                
            case 4
                % 阶段4: 整理数据集
                fprintf('执行多标注文件批量处理...\n');
                if exist('multi_label_process_2.m', 'file')
                    run('multi_label_process_2.m');
                end
                
                fprintf('验证最终结果...\n');
                if exist('verify_results_3.m', 'file')
                    run('verify_results_3.m');
                end
                
            otherwise
                warning('未知的阶段编号: %d', stage);
        end
        
    catch ME
        cd(originalDir);
        rethrow(ME);
    end
    
    cd(originalDir);
end

function showProgress(current, total, description)
%SHOWPROGRESS 显示进度条
    percentage = current / total * 100;
    barLength = 50;
    filledLength = round(barLength * current / total);
    
    bar = repmat('█', 1, filledLength);
    bar = [bar, repmat('░', 1, barLength - filledLength)];
    
    fprintf('[%s] %.1f%% - %s\n', bar, percentage, description);
end

function performQualityCheck(stage, config)
%PERFORMQUALITYCHECK 执行质量检查
    if isfield(config, 'quality_control') && isfield(config.quality_control, 'checkpoints')
        checkpoints = config.quality_control.checkpoints;
        
        for i = 1:length(checkpoints)
            if checkpoints(i).stage == stage
                fprintf('执行质量检查: %s\n', checkpoints(i).name);
                % 这里可以添加具体的质量检查逻辑
                break;
            end
        end
    end
end

function logMessage(logFile, message)
%LOGMESSAGE 记录日志消息
    if ~isempty(logFile)
        fid = fopen(logFile, 'a');
        if fid ~= -1
            fprintf(fid, '[%s] %s\n', datestr(now), message);
            fclose(fid);
        end
    end
end

function generateFinalReport(config, params, logFile)
%GENERATEFINALREPORT 生成最终报告
    fprintf('\n=== 工作流程执行报告 ===\n');
    
    % 统计各阶段文件
    for stage = params.start_stage:params.end_stage
        if ~ismember(stage, params.skip_stages)
            try
                report = file_manager('generate_report', stage);
                fprintf('阶段%d文件统计:\n', stage);
                fprintf('  处理数据: %d个文件\n', report.processed_data.count);
            catch
                fprintf('阶段%d: 无法生成报告\n', stage);
            end
        end
    end
    
    fprintf('\n执行参数:\n');
    fprintf('  执行时间: %s\n', datestr(now));
    fprintf('  日志文件: %s\n', logFile);
    
    fprintf('\n✓ 报告生成完成\n');
end

function str = yesno(value)
%YESNO 将逻辑值转换为是/否字符串
    if value
        str = '是';
    else
        str = '否';
    end
end
