# 肠鸣音信号标注工作流程自动化工具使用指南

## 📋 概述

本指南介绍如何使用新创建的自动化工具来优化肠鸣音信号标注工作流程。这些工具可以将手动操作减少80%以上，提高工作效率和数据一致性。

## 🚀 快速开始

### 1. 一键执行完整工作流程
```matlab
% 最简单的使用方式 - 执行所有5个阶段
workflow_controller();
```

### 2. 预演模式（推荐首次使用）
```matlab
% 预演模式不会实际执行，只显示执行流程
workflow_controller('dry_run', true);
```

### 3. 交互式模式
```matlab
% 每个阶段前都会询问是否继续
workflow_controller('interactive', true);
```

## 🛠️ 核心工具介绍

### 1. workflow_controller.m - 主控制器
**功能**: 一键式执行整个工作流程
**特点**:
- 支持从任意阶段开始执行
- 可跳过特定阶段
- 完整的进度显示和错误处理
- 自动生成执行日志

**常用参数**:
```matlab
workflow_controller('start_stage', 2);              % 从阶段2开始
workflow_controller('skip_stages', [1, 3]);         % 跳过阶段1和3
workflow_controller('continue_on_error', true);     % 遇到错误继续执行
```

### 2. file_manager.m - 文件管理器
**功能**: 自动管理各阶段间的文件传递
**特点**:
- 自动检测和复制输出文件
- 文件完整性验证
- 智能备份和清理
- 详细的文件报告

**常用操作**:
```matlab
file_manager('copy_stage_outputs', 0, 1);           % 复制阶段0输出到阶段1
file_manager('validate_files', file_list);          % 验证文件完整性
file_manager('cleanup_temp', 2);                    % 清理阶段2临时文件
file_manager('generate_report', 3);                 % 生成阶段3文件报告
```

### 3. config_manager.m - 配置管理器
**功能**: 统一管理所有配置参数
**特点**:
- 集中式配置管理
- 配置验证和备份
- 动态参数修改
- 版本控制支持

**常用操作**:
```matlab
config = config_manager('load');                    % 加载配置
config_manager('backup');                           % 备份配置
fs = config_manager('get', 'global_settings.sampling_rate'); % 获取采样率
config_manager('set', 'global_settings.parallel_processing', true); % 启用并行
```

## 📊 使用场景示例

### 场景1: 完整数据处理流程
```matlab
% 适用于新数据的完整处理
workflow_controller('auto_backup', true);
```

### 场景2: 重新处理特定阶段
```matlab
% 只重新执行阶段2和3
workflow_controller('start_stage', 2, 'end_stage', 3);
```

### 场景3: 跳过人工标注阶段
```matlab
% 跳过需要人工操作的阶段1
workflow_controller('skip_stages', [1]);
```

### 场景4: 批量数据处理
```matlab
% 启用并行处理，提高批量处理速度
config_manager('set', 'global_settings.parallel_processing', true);
workflow_controller('continue_on_error', true);
```

## ⚙️ 配置文件说明

### workflow_config.json 主要配置项

#### 全局设置
```json
"global_settings": {
  "sampling_rate": 2570,           // 采样率
  "segment_length_seconds": 60,    // 分段长度
  "auto_file_copy": true,          // 自动文件复制
  "backup_enabled": true,          // 启用备份
  "parallel_processing": false     // 并行处理
}
```

#### 阶段设置
每个阶段都有独立的配置，包括：
- 主要脚本文件名
- 算法参数
- 输入输出文件夹
- 处理选项

#### 质量控制
```json
"quality_control": {
  "checkpoints": [                 // 质量检查点
    {
      "stage": 0,
      "checks": ["file_integrity", "signal_quality"]
    }
  ]
}
```

## 🔧 高级功能

### 1. 自定义配置文件
```matlab
% 使用自定义配置文件
workflow_controller('config_file', 'my_config.json');
```

### 2. 错误恢复机制
```matlab
% 启用错误恢复，遇到问题时自动重试
workflow_controller('continue_on_error', true);
```

### 3. 符号链接模式
```matlab
% 使用符号链接节省存储空间
file_manager('create_symlinks', source_dir, target_dir);
```

### 4. 批量文件验证
```matlab
% 验证整个阶段的文件完整性
files = file_manager('detect_outputs', 2);
result = file_manager('validate_files', files);
```

## 📈 性能优化建议

### 1. 内存优化
- 处理大文件时分批进行
- 及时清理临时文件
- 监控内存使用情况

### 2. 存储优化
- 启用自动备份但定期清理旧备份
- 使用符号链接减少重复存储
- 压缩不常用的数据文件

### 3. 处理速度优化
- 根据硬件配置启用并行处理
- 跳过不必要的验证步骤
- 使用SSD存储提高I/O速度

## 🚨 故障排除

### 常见问题及解决方案

#### 1. 配置文件错误
**症状**: 加载配置失败
**解决**: 
```matlab
config_manager('reset');  % 重置为默认配置
```

#### 2. 文件权限问题
**症状**: 无法复制或创建文件
**解决**: 检查MATLAB运行权限，确保对工作目录有读写权限

#### 3. 内存不足
**症状**: 处理大文件时内存溢出
**解决**: 
```matlab
config_manager('set', 'performance.memory_limit_gb', 2);  % 降低内存限制
```

#### 4. 脚本文件缺失
**症状**: 找不到阶段脚本文件
**解决**: 确保各阶段目录下的脚本文件完整

#### 5. 数据格式不兼容
**症状**: 文件验证失败
**解决**: 检查数据文件格式，确保符合预期结构

## 📝 最佳实践

### 1. 使用前准备
- [ ] 运行 `workflow_demo.m` 熟悉工具
- [ ] 使用预演模式测试流程
- [ ] 备份重要数据和配置
- [ ] 检查磁盘空间是否充足

### 2. 执行过程中
- [ ] 监控执行日志
- [ ] 定期检查中间结果
- [ ] 保持足够的磁盘空间
- [ ] 避免中断长时间运行的任务

### 3. 执行完成后
- [ ] 验证最终结果
- [ ] 清理临时文件
- [ ] 备份重要输出
- [ ] 记录处理参数和结果

## 📚 参考资料

### 相关文件
- `README.md` - 完整工作流程说明
- `workflow_demo.m` - 使用示例脚本
- `workflow_config.json` - 配置文件模板

### 函数文档
每个函数都包含详细的帮助文档，使用以下命令查看：
```matlab
help workflow_controller
help file_manager
help config_manager
```

## 🆘 获取帮助

如果遇到问题或需要更多帮助：

1. 查看函数内置帮助文档
2. 运行 `workflow_demo.m` 查看使用示例
3. 检查生成的日志文件
4. 验证配置文件格式
5. 确认所有依赖文件存在

---

**版本**: 1.0  
**更新日期**: 2024-08-22  
**作者**: Augment Agent

*这些自动化工具完全兼容现有工作流程，不会修改任何原有脚本文件。*
