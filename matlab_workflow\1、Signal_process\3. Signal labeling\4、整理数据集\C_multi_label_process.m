%% MULTI_LABEL_PROCESS 多标注文件批量处理程序（适配版）
%   批量处理4、Label文件夹中的标注文件，提取对应的信号数据片段并统一保存。
%   主要用于肠鸣音信号分析项目中的数据集构建和管理。
%
%   Description:
%   该脚本处理4、Label文件夹中的ls_data3.mat标注文件，
%   从标注文件中提取标签信息和对应的原始信号文件路径，
%   根据ROILimits时间范围从1、Raw data文件夹中的原始信号文件中提取相应的数据片段，
%   并按照统一的命名规则保存到2、Processed data文件夹中。
%
%   文件夹结构:
%   - 4、Label/ls_data3.mat: labeledSignalSet对象，包含标注信息
%   - 1、Raw data/*.mat: 原始分段信号文件（每段60秒）
%   - 2、Processed data/: 输出文件夹（提取的信号片段）
%   - 3、Backup/: 备份文件夹
%
%   标注文件结构:
%   - ls: labeledSignalSet对象
%     - ls.Labels: 包含Row和BS列的表格
%     - Row: 信号名称（如'data3_5min_seg002_tt1'）
%     - BS: 标注表，包含ROILimits和Value字段
%
%   原始数据文件结构:
%   - 文件名格式: data{N}_5min_seg{XXX}_tt.mat
%   - 包含变量: tt1（时间表格式的信号数据）
%
%   输出文件结构:
%   - 文件命名格式: "{dataset}_seg{XXX}_{channel}_{label}_{index}.mat"
%   - 每个文件包含extractedData结构体：
%     - originalSignal: 原始时间轴的信号数据
%     - restoredSignal: 还原时间轴的信号数据（考虑分段偏移）
%     - labelInfo: 标注信息（标签值、时间范围等）
%
%   时间轴还原规则:
%   - seg001: [0,60]秒 → [0,60]秒（无偏移）
%   - seg002: [0,60]秒 → [60,120]秒（偏移60秒）
%   - seg003: [0,60]秒 → [120,180]秒（偏移120秒）
%   - 通用公式：还原时间 = 原始时间 + (段号-1) × 60
%
%   Processing Steps:
%   1. 加载4、Label/ls_data3.mat标注文件
%   2. 创建2、Processed data输出目录
%   3. 遍历ls.Labels中的每个信号：
%      a. 解析信号名称获取数据集、段号和通道信息
%      b. 构造对应的原始数据文件路径
%      c. 加载原始信号数据
%      d. 遍历该信号的所有标注
%      e. 根据ROILimits提取信号片段
%      f. 计算还原时间轴
%      g. 保存提取的数据到输出文件夹
%   4. 显示处理进度和统计信息
%
%   Requirements:
%   - 标注文件必须是labeledSignalSet对象格式
%   - 原始信号文件必须包含tt1变量（时间表格式）
%   - 文件夹结构必须符合预期布局
%
%   Example Usage:
%   1. 确保4、Label/ls_data3.mat文件存在
%   2. 确保1、Raw data文件夹包含对应的原始数据文件
%   3. 运行此脚本: multi_label_process
%   4. 检查2、Processed data文件夹中的输出结果
%
%   Notes:
%   - 脚本会自动创建输出目录
%   - 处理过程中会显示详细的进度信息
%   - 如果遇到无效的标注表，会发出警告并跳过
%   - 支持时间轴还原，便于后续分析
%   - 建议在处理前备份原始数据
%
%   See also: LOAD, SAVE, TIMETABLE, TIMERANGE, PARSESIGNALNAME

clear;
clc;
close all;

%% 配置路径
labelFile = fullfile('4、Label', 'ls_data3.mat');
rawDataDir = '1、Raw data';
outputDir = '2、Processed data';

% 创建输出目录
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
end

%% 加载标注文件
fprintf('正在加载标注文件: %s\n', labelFile);

% 检查标注文件是否存在
if ~exist(labelFile, 'file')
    error('标注文件不存在: %s', labelFile);
end

try
    load(labelFile, 'ls');
    fprintf('✓ 标注文件加载成功\n');
catch ME
    error('标注文件加载失败: %s', ME.message);
end

% 检查ls对象是否存在
if ~exist('ls', 'var')
    error('标注文件中不存在ls变量');
end

% 检查ls对象是否为labeledSignalSet
if ~isa(ls, 'labeledSignalSet')
    error('ls变量不是labeledSignalSet对象，而是: %s', class(ls));
end

%% 获取标注信息
labels = ls.Labels;
fprintf('找到 %d 个标注信号\n', height(labels));

%% 初始化计数器
totalExtractedFiles = 0;
totalLabels = 0;
processedSignals = 0;

%% 提取标注数据的主循环
fprintf('\n开始处理标注数据...\n');

for i = 1:height(labels)
    % 获取当前行的信号名称和标注表
    signalName = labels.Row{i};  % 例如：'data3_5min_seg002_tt1'
    bsTable = labels{i, 'BS'}{1}; % 获取标注表

    % 检查是否有标注数据
    if isempty(bsTable) || height(bsTable) == 0
        fprintf('  跳过信号 %s：无标注数据\n', signalName);
        continue;
    end

    processedSignals = processedSignals + 1;
    fprintf('\n处理信号 %d/%d: %s\n', processedSignals, height(labels), signalName);
    fprintf('  找到 %d 个标注\n', height(bsTable));

    % 解析信号名称以获取数据集、段号和通道信息
    try
        [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName);
    catch ME
        warning('无法解析信号名称 %s: %s', signalName, ME.message);
        continue;
    end

    % 构造对应的原始数据文件路径
    rawFileName = sprintf('%s_seg%03d_tt.mat', datasetInfo, segmentNum);
    rawFilePath = fullfile(rawDataDir, rawFileName);

    if ~exist(rawFilePath, 'file')
        warning('原始数据文件不存在: %s', rawFilePath);
        continue;
    end

    % 加载原始数据
    fprintf('  加载原始数据: %s\n', rawFileName);
    try
        rawData = load(rawFilePath);
    catch ME
        warning('无法加载原始数据文件 %s: %s', rawFilePath, ME.message);
        continue;
    end

    % 检查是否包含对应的信号变量
    % 原始数据文件中的变量名就是完整的信号名称，如 'data3_5min_seg002_tt1'
    if ~isfield(rawData, signalName)
        warning('原始数据文件 %s 中不包含变量 %s', rawFilePath, signalName);
        fprintf('    可用变量: %s\n', strjoin(fieldnames(rawData), ', '));
        continue;
    end

    signalData = rawData.(signalName);

    % 处理每个标注
    for j = 1:height(bsTable)
        totalLabels = totalLabels + 1;

        % 获取标注信息
        roiLimits = bsTable.ROILimits(j, :);
        labelValue = bsTable.Value(j);
        if iscell(labelValue)
            labelValue = labelValue{1};
        end

        % 计算还原后的真实时间
        segmentOffset = (segmentNum - 1) * 60; % 每段60秒
        realStartTime = roiLimits(1) + segmentOffset;
        realEndTime = roiLimits(2) + segmentOffset;

        fprintf('    标注 %d/%d: %.3f-%.3f秒 (原始) → %.3f-%.3f秒 (还原), 标签: %s\n', ...
            j, height(bsTable), roiLimits(1), roiLimits(2), realStartTime, realEndTime, labelValue);

        % 提取信号片段
        try
            extractedSignal = signalData(timerange(seconds(roiLimits(1)), seconds(roiLimits(2))), :);

            % 创建还原时间的时间表
            originalTime = extractedSignal.Time;
            restoredTime = originalTime + seconds(segmentOffset);

            % 创建包含还原时间的新时间表，保持Time作为时间列名
            restoredSignal = extractedSignal;  % 复制原始结构
            restoredSignal.Time = restoredTime;  % 更新时间列

            % 准备保存的数据结构
            extractedData = struct();
            extractedData.originalSignal = extractedSignal;      % 原始时间的信号
            extractedData.restoredSignal = restoredSignal;       % 还原时间的信号
            extractedData.labelInfo = struct();
            extractedData.labelInfo.value = labelValue;
            extractedData.labelInfo.originalTimeRange = roiLimits;
            extractedData.labelInfo.restoredTimeRange = [realStartTime, realEndTime];
            extractedData.labelInfo.sourceFile = signalName;
            extractedData.labelInfo.segmentNumber = segmentNum;
            extractedData.labelInfo.channel = channelInfo;

            % 构造输出文件名
            outputFileName = sprintf('%s_seg%03d_%s_%s_%03d.mat', ...
                datasetInfo, segmentNum, channelInfo, labelValue, j);
            outputFilePath = fullfile(outputDir, outputFileName);

            % 保存数据
            save(outputFilePath, 'extractedData');
            totalExtractedFiles = totalExtractedFiles + 1;

            fprintf('      已保存: %s\n', outputFileName);

        catch ME
            warning('SIGNAL_EXTRACTION:Error', '提取信号时出错: %s', ME.message);
        end
    end
end

%% 生成处理报告
fprintf('\n=== 处理完成 ===\n');
fprintf('处理的信号数量: %d\n', processedSignals);
fprintf('总标注数量: %d\n', totalLabels);
fprintf('成功提取: %d\n', totalExtractedFiles);
fprintf('输出目录: %s\n', outputDir);

% 保存处理报告
reportData = struct();
reportData.processTime = datetime('now');
reportData.processedSignals = processedSignals;
reportData.totalLabels = totalLabels;
reportData.extractedCount = totalExtractedFiles;
reportData.sourceFile = labelFile;
reportData.outputDir = outputDir;

% reportFilePath = fullfile(outputDir, 'extraction_report.mat');
% save(reportFilePath, 'reportData');
% fprintf('处理报告已保存: %s\n', reportFilePath);

%% 辅助函数：解析信号名称
function [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
%PARSESIGNALNAME 解析肠鸣音信号文件名并提取关键信息
%   从标准化的信号文件名中提取数据集标识、段号和通道信息，
%   支持肠鸣音信号分析系统的文件命名规范。
%
%   Syntax:
%   [datasetInfo, segmentNum, channelInfo] = parseSignalName(signalName)
%
%   Inputs:
%   signalName - 信号文件名字符串 (char/string)
%                格式：'data{N}_5min_seg{XXX}_{channel}'
%                例如：'data3_5min_seg002_tt1'
%
%   Outputs:
%   datasetInfo - 数据集标识字符串 (char)
%                 例如：'data3_5min'
%   segmentNum  - 段号数值 (double)
%                 例如：2 (从seg002中提取)
%   channelInfo - 通道标识字符串 (char)
%                 例如：'tt1'
%
%   Algorithm:
%   使用正则表达式模式匹配：'(data\d+_5min)_seg(\d+)_(tt\d+)'
%   - 第一组：数据集标识（data数字_5min）
%   - 第二组：段号（数字部分）
%   - 第三组：通道标识（tt数字）
%
%   Example:
%   [dataset, segNum, channel] = parseSignalName('data3_5min_seg002_tt1');
%   % 返回：dataset = 'data3_5min', segNum = 2, channel = 'tt1'
%
%   Error Handling:
%   如果输入格式不符合预期模式，将抛出错误信息
%
%   See also: MULTI_LABEL_PROCESS, REGEXP

    % 使用正则表达式解析标准文件名格式
    pattern = '(data\d+_5min)_seg(\d+)_(tt\d+)';
    tokens = regexp(signalName, pattern, 'tokens');

    if isempty(tokens)
        error('PARSESIGNALNAME:InvalidFormat', ...
              '无法解析信号名称: %s\n期望格式: data{N}_5min_seg{XXX}_{channel}', signalName);
    end

    % 提取解析结果
    datasetInfo = tokens{1}{1};
    segmentNum = str2double(tokens{1}{2});
    channelInfo = tokens{1}{3};
end
