%% 肠鸣音信号标注工作流程自动化工具 - 快速入门
%QUICK_START 快速入门脚本，帮助用户快速上手自动化工具
%   本脚本提供了一个简单的向导式界面，引导用户完成基本的设置和使用。
%
%   作者: Augment Agent
%   日期: 2024-08-22
%   版本: 1.0

function quick_start()
    fprintf('\n');
    fprintf('╔══════════════════════════════════════════════════════════════╗\n');
    fprintf('║          肠鸣音信号标注工作流程自动化工具 - 快速入门          ║\n');
    fprintf('╚══════════════════════════════════════════════════════════════╝\n');
    fprintf('\n');
    
    fprintf('欢迎使用肠鸣音信号标注工作流程自动化工具！\n');
    fprintf('本向导将帮助您快速上手使用这些工具。\n\n');
    
    % 检查当前目录
    currentDir = pwd;
    [~, folderName] = fileparts(currentDir);
    
    if ~strcmp(folderName, '5、workflow')
        fprintf('⚠️  注意：您当前不在5、workflow目录中。\n');
        fprintf('当前目录：%s\n', currentDir);
        
        % 尝试切换到正确目录
        workflowDir = fullfile(fileparts(currentDir), '5、workflow');
        if exist(workflowDir, 'dir')
            fprintf('正在切换到正确目录...\n');
            cd(workflowDir);
            fprintf('✓ 已切换到：%s\n\n', pwd);
        else
            fprintf('❌ 找不到5、workflow目录，请手动切换到正确目录后重新运行。\n');
            return;
        end
    else
        fprintf('✓ 您已在正确的目录中：%s\n\n', currentDir);
    end
    
    % 主菜单循环
    while true
        showMainMenu();
        choice = input('请选择操作 (输入数字): ', 's');
        
        switch choice
            case '1'
                runInstallationCheck();
            case '2'
                runDemo();
            case '3'
                runDryRun();
            case '4'
                runFullWorkflow();
            case '5'
                showConfiguration();
            case '6'
                showHelp();
            case '0'
                fprintf('\n感谢使用！再见！\n');
                break;
            otherwise
                fprintf('\n❌ 无效选择，请重新输入。\n\n');
        end
        
        if ~strcmp(choice, '0')
            fprintf('\n按任意键继续...\n');
            pause;
        end
    end
end

function showMainMenu()
    fprintf('┌─────────────────────────────────────────────────────────────┐\n');
    fprintf('│                          主菜单                            │\n');
    fprintf('├─────────────────────────────────────────────────────────────┤\n');
    fprintf('│  1. 验证安装和配置                                         │\n');
    fprintf('│  2. 查看使用示例和演示                                     │\n');
    fprintf('│  3. 运行预演模式（推荐首次使用）                           │\n');
    fprintf('│  4. 执行完整工作流程                                       │\n');
    fprintf('│  5. 查看和修改配置                                         │\n');
    fprintf('│  6. 帮助和文档                                             │\n');
    fprintf('│  0. 退出                                                   │\n');
    fprintf('└─────────────────────────────────────────────────────────────┘\n');
    fprintf('\n');
end

function runInstallationCheck()
    fprintf('\n=== 验证安装和配置 ===\n');
    fprintf('正在检查安装状态...\n\n');
    
    try
        verify_installation();
        fprintf('\n✓ 安装验证完成！\n');
    catch ME
        fprintf('\n❌ 安装验证失败：%s\n', ME.message);
        fprintf('建议：请检查所有文件是否正确安装。\n');
    end
end

function runDemo()
    fprintf('\n=== 使用示例和演示 ===\n');
    fprintf('正在运行演示程序...\n\n');
    
    try
        workflow_demo();
        fprintf('\n✓ 演示完成！\n');
    catch ME
        fprintf('\n❌ 演示运行失败：%s\n', ME.message);
    end
end

function runDryRun()
    fprintf('\n=== 预演模式 ===\n');
    fprintf('预演模式将模拟执行工作流程，但不会实际处理数据。\n');
    fprintf('这是了解工作流程的最佳方式。\n\n');
    
    response = input('是否继续运行预演模式？(y/n): ', 's');
    if strcmpi(response, 'y')
        try
            fprintf('\n开始预演模式...\n');
            workflow_controller('dry_run', true);
            fprintf('\n✓ 预演模式完成！\n');
        catch ME
            fprintf('\n❌ 预演模式失败：%s\n', ME.message);
        end
    else
        fprintf('已取消预演模式。\n');
    end
end

function runFullWorkflow()
    fprintf('\n=== 执行完整工作流程 ===\n');
    fprintf('⚠️  警告：这将执行完整的数据处理流程！\n');
    fprintf('请确保：\n');
    fprintf('1. 已经准备好输入数据\n');
    fprintf('2. 有足够的磁盘空间\n');
    fprintf('3. 已经备份重要数据\n\n');
    
    response = input('确定要执行完整工作流程吗？(yes/no): ', 's');
    if strcmpi(response, 'yes')
        fprintf('\n选择执行模式：\n');
        fprintf('1. 标准模式（推荐）\n');
        fprintf('2. 交互模式（每个阶段前询问）\n');
        fprintf('3. 自定义模式\n');
        
        mode = input('请选择模式 (1-3): ', 's');
        
        try
            switch mode
                case '1'
                    fprintf('\n开始标准模式执行...\n');
                    workflow_controller();
                case '2'
                    fprintf('\n开始交互模式执行...\n');
                    workflow_controller('interactive', true);
                case '3'
                    runCustomWorkflow();
                otherwise
                    fprintf('无效选择，使用标准模式。\n');
                    workflow_controller();
            end
            fprintf('\n✓ 工作流程执行完成！\n');
        catch ME
            fprintf('\n❌ 工作流程执行失败：%s\n', ME.message);
        end
    else
        fprintf('已取消执行。\n');
    end
end

function runCustomWorkflow()
    fprintf('\n=== 自定义执行模式 ===\n');
    
    % 获取起始阶段
    startStage = input('起始阶段 (0-4, 默认0): ', 's');
    if isempty(startStage)
        startStage = 0;
    else
        startStage = str2double(startStage);
    end
    
    % 获取结束阶段
    endStage = input('结束阶段 (0-4, 默认4): ', 's');
    if isempty(endStage)
        endStage = 4;
    else
        endStage = str2double(endStage);
    end
    
    % 获取跳过的阶段
    skipStages = input('跳过的阶段 (例如: 1,3 或直接回车): ', 's');
    if ~isempty(skipStages)
        skipStages = str2num(skipStages); %#ok<ST2NM>
    else
        skipStages = [];
    end
    
    fprintf('\n执行参数：\n');
    fprintf('起始阶段: %d\n', startStage);
    fprintf('结束阶段: %d\n', endStage);
    if ~isempty(skipStages)
        fprintf('跳过阶段: %s\n', mat2str(skipStages));
    end
    
    response = input('\n确认执行？(y/n): ', 's');
    if strcmpi(response, 'y')
        if ~isempty(skipStages)
            workflow_controller('start_stage', startStage, 'end_stage', endStage, 'skip_stages', skipStages);
        else
            workflow_controller('start_stage', startStage, 'end_stage', endStage);
        end
    else
        fprintf('已取消执行。\n');
    end
end

function showConfiguration()
    fprintf('\n=== 配置管理 ===\n');
    
    try
        config = config_manager('load');
        
        fprintf('当前配置信息：\n');
        fprintf('采样率: %d Hz\n', config.global_settings.sampling_rate);
        fprintf('分段长度: %d 秒\n', config.global_settings.segment_length_seconds);
        fprintf('自动文件复制: %s\n', yesno(config.global_settings.auto_file_copy));
        fprintf('备份功能: %s\n', yesno(config.global_settings.backup_enabled));
        fprintf('并行处理: %s\n', yesno(config.global_settings.parallel_processing));
        
        fprintf('\n配置操作：\n');
        fprintf('1. 备份当前配置\n');
        fprintf('2. 重置为默认配置\n');
        fprintf('3. 修改采样率\n');
        fprintf('4. 切换并行处理\n');
        fprintf('0. 返回主菜单\n');
        
        choice = input('请选择操作: ', 's');
        
        switch choice
            case '1'
                config_manager('backup');
                fprintf('✓ 配置已备份\n');
            case '2'
                response = input('确定要重置配置吗？(y/n): ', 's');
                if strcmpi(response, 'y')
                    config_manager('reset');
                    fprintf('✓ 配置已重置\n');
                end
            case '3'
                newFs = input('请输入新的采样率 (当前: %d): ', config.global_settings.sampling_rate);
                if ~isempty(newFs) && isnumeric(newFs) && newFs > 0
                    config_manager('set', 'global_settings.sampling_rate', newFs);
                    fprintf('✓ 采样率已更新为 %d Hz\n', newFs);
                end
            case '4'
                currentParallel = config.global_settings.parallel_processing;
                config_manager('set', 'global_settings.parallel_processing', ~currentParallel);
                fprintf('✓ 并行处理已%s\n', yesno(~currentParallel));
        end
        
    catch ME
        fprintf('❌ 配置操作失败：%s\n', ME.message);
    end
end

function showHelp()
    fprintf('\n=== 帮助和文档 ===\n');
    fprintf('可用的帮助资源：\n\n');
    
    fprintf('📖 文档文件：\n');
    fprintf('• README.md - 自动化工具概述\n');
    fprintf('• AUTOMATION_GUIDE.md - 详细使用指南\n\n');
    
    fprintf('🔧 工具脚本：\n');
    fprintf('• workflow_demo.m - 完整使用示例\n');
    fprintf('• test_automation_tools.m - 功能测试\n');
    fprintf('• verify_installation.m - 安装验证\n\n');
    
    fprintf('💡 快速命令参考：\n');
    fprintf('• workflow_controller() - 执行完整流程\n');
    fprintf('• workflow_controller(''dry_run'', true) - 预演模式\n');
    fprintf('• config_manager(''load'') - 加载配置\n');
    fprintf('• file_manager(''generate_report'', 0) - 生成报告\n\n');
    
    fprintf('🆘 获取更多帮助：\n');
    fprintf('• 运行 help function_name 查看函数帮助\n');
    fprintf('• 查看生成的日志文件了解详细信息\n');
    fprintf('• 检查各阶段的code_analysis_report.md文件\n\n');
end

function str = yesno(value)
    if value
        str = '是';
    else
        str = '否';
    end
end
