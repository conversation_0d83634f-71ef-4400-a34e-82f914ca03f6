%% 标注数据提取结果验证脚本
%VALIDATE_EXTRACTION_3 验证标注数据提取结果的正确性和完整性
%   本脚本对label_process_2.m生成的提取数据进行全面验证，
%   包括数据结构完整性、时间轴还原准确性和信号质量检查。
%
%   验证功能：
%   1. 处理报告分析和统计信息显示
%   2. 提取数据文件的结构完整性验证
%   3. 时间轴还原准确性检查
%   4. 标签分类和分布统计
%   5. 信号质量和完整性验证
%   6. 可视化时间分布和标签分析
%
%   验证内容：
%   - 数据文件加载成功率
%   - 必需字段存在性检查
%   - 时间范围计算准确性
%   - 标签值格式一致性
%   - 段号和通道信息正确性
%
%   输入要求：
%   - 2、Processed data/*.mat (提取的数据文件)
%   - 2、Processed data/extraction_report.mat (处理报告)
%
%   输出结果：
%   - 详细的验证报告
%   - 统计分析结果
%   - 时间分布可视化图表
%   - 保存的验证图像文件
%
%   使用说明：
%   在使用提取的数据进行分析前，建议运行此验证脚本
%   以确保数据质量和完整性
%
%   作者：肠鸣音信号分析团队
%   日期：2025年
%   版本：3.0
%
%   See also: LABEL_PROCESS_2, TEST_EXTRACTION_1, DEMO_USAGE_4

clear;
clc;
close all;

%% 配置
processedDataDir = '2、Processed data';
reportFile = fullfile(processedDataDir, 'extraction_report.mat');

%% 检查处理报告
if exist(reportFile, 'file')
    load(reportFile);
    fprintf('=== 提取报告 ===\n');
    fprintf('处理时间: %s\n', char(reportData.processTime));
    fprintf('总标注数量: %d\n', reportData.totalLabels);
    fprintf('成功提取: %d\n', reportData.extractedCount);
    fprintf('成功率: %.1f%%\n', reportData.extractedCount/reportData.totalLabels*100);
else
    fprintf('未找到处理报告文件\n');
end

%% 获取所有提取的数据文件
dataFiles = dir(fullfile(processedDataDir, '*.mat'));
dataFiles = dataFiles(~strcmp({dataFiles.name}, 'extraction_report.mat'));

fprintf('\n=== 提取的数据文件 ===\n');
fprintf('找到 %d 个数据文件\n', length(dataFiles));

%% 验证每个提取的数据文件
validationResults = struct();
validationResults.files = {};
validationResults.timeRanges = [];
validationResults.labels = {};
validationResults.segments = [];
validationResults.channels = {};

for i = 1:length(dataFiles)
    fileName = dataFiles(i).name;
    filePath = fullfile(processedDataDir, fileName);
    
    try
        % 加载数据
        data = load(filePath);
        extractedData = data.extractedData;
        
        % 验证数据结构
        requiredFields = {'originalSignal', 'restoredSignal', 'labelInfo'};
        hasAllFields = all(isfield(extractedData, requiredFields));
        
        if hasAllFields
            % 提取信息
            labelInfo = extractedData.labelInfo;
            originalRange = labelInfo.originalTimeRange;
            restoredRange = labelInfo.restoredTimeRange;
            
            % 存储验证结果
            validationResults.files{end+1} = fileName;
            validationResults.timeRanges(end+1, :) = restoredRange;
            % 确保标签值是字符串
            labelValue = labelInfo.value;
            if iscell(labelValue)
                labelValue = labelValue{1};
            end
            if ~ischar(labelValue) && ~isstring(labelValue)
                labelValue = char(labelValue);
            end
            validationResults.labels{end+1} = char(labelValue);
            validationResults.segments(end+1) = labelInfo.segmentNumber;
            validationResults.channels{end+1} = labelInfo.channel;
            
            fprintf('✓ %s: %.3f-%.3f秒, 标签:%s, 段:%d, 通道:%s\n', ...
                fileName, restoredRange(1), restoredRange(2), ...
                labelInfo.value, labelInfo.segmentNumber, labelInfo.channel);
        else
            fprintf('✗ %s: 数据结构不完整\n', fileName);
        end
        
    catch ME
        fprintf('✗ %s: 加载失败 - %s\n', fileName, ME.message);
    end
end

%% 统计分析
if ~isempty(validationResults.files)
    fprintf('\n=== 统计分析 ===\n');
    
    % 标签统计
    uniqueLabels = unique(validationResults.labels);
    fprintf('标签类型: ');
    for i = 1:length(uniqueLabels)
        labelCount = sum(strcmp(validationResults.labels, uniqueLabels{i}));
        fprintf('%s(%d) ', uniqueLabels{i}, labelCount);
    end
    fprintf('\n');
    
    % 段号统计
    uniqueSegments = unique(validationResults.segments);
    fprintf('涉及段号: ');
    fprintf('%d ', uniqueSegments);
    fprintf('\n');
    
    % 通道统计
    uniqueChannels = unique(validationResults.channels);
    fprintf('通道类型: ');
    for i = 1:length(uniqueChannels)
        channelCount = sum(strcmp(validationResults.channels, uniqueChannels{i}));
        fprintf('%s(%d) ', uniqueChannels{i}, channelCount);
    end
    fprintf('\n');
    
    % 时间范围统计
    timeRanges = validationResults.timeRanges;
    fprintf('时间范围: %.3f - %.3f 秒\n', min(timeRanges(:,1)), max(timeRanges(:,2)));
    fprintf('平均持续时间: %.3f 秒\n', mean(timeRanges(:,2) - timeRanges(:,1)));
end

%% 可视化时间分布（如果有数据）
if ~isempty(validationResults.files)
    figure('Name', '标注时间分布', 'Position', [100, 100, 1000, 600]);
    
    % 为不同标签分配颜色
    colors = lines(length(uniqueLabels));
    colorMap = containers.Map(uniqueLabels, num2cell(colors, 2));
    
    subplot(2,1,1);
    hold on;
    for i = 1:length(validationResults.files)
        timeRange = validationResults.timeRanges(i, :);
        label = validationResults.labels{i};
        color = colorMap(label);
        
        % 绘制时间段
        plot(timeRange, [i, i], 'LineWidth', 3, 'Color', color);
        plot(timeRange(1), i, 'o', 'MarkerSize', 6, 'Color', color, 'MarkerFaceColor', color);
        plot(timeRange(2), i, 's', 'MarkerSize', 6, 'Color', color, 'MarkerFaceColor', color);
    end
    
    xlabel('时间 (秒)');
    ylabel('标注序号');
    title('标注时间分布图');
    grid on;
    
    % 添加图例
    legendEntries = {};
    legendColors = [];
    for i = 1:length(uniqueLabels)
        legendEntries{end+1} = uniqueLabels{i};
        legendColors(end+1, :) = colorMap(uniqueLabels{i});
    end
    
    % 创建图例
    for i = 1:length(uniqueLabels)
        plot(NaN, NaN, 'LineWidth', 3, 'Color', legendColors(i,:), 'DisplayName', uniqueLabels{i});
    end
    legend('Location', 'best');
    
    % 时间分布直方图
    subplot(2,1,2);
    allStartTimes = validationResults.timeRanges(:,1);
    histogram(allStartTimes, 20, 'FaceAlpha', 0.7);
    xlabel('开始时间 (秒)');
    ylabel('频次');
    title('标注开始时间分布直方图');
    grid on;
    
    % 保存图像
    % saveas(gcf, fullfile(processedDataDir, 'validation_plot.png'));
    % fprintf('\n可视化结果已保存: %s\n', fullfile(processedDataDir, 'validation_plot.png'));
end

fprintf('\n=== 验证完成 ===\n');
