%% MATLAB数据文件分析脚本
% 分析data9_5min_seg001_tt.mat和data9_5min_seg002_tt.mat的差异
% 目的：找出seg002文件在信号标记器中显示异常的原因

clear; clc; close all;

%% 文件路径定义
file1_path = 'matlab_workflow/1、Signal_process/3. Signal labeling/1、信号标注器APP标注/1、Raw data/data9_5min_seg001_tt.mat';
file2_path = 'matlab_workflow/1、Signal_process/3. Signal labeling/1、信号标注器APP标注/1、Raw data/data9_5min_seg002_tt.mat';

fprintf('=== MATLAB数据文件分析报告 ===\n');
fprintf('分析时间: %s\n', datestr(now));
fprintf('文件1 (正常): %s\n', file1_path);
fprintf('文件2 (异常): %s\n', file2_path);
fprintf('=====================================\n\n');

%% 1. 检查文件存在性和基本信息
fprintf('1. 文件基本信息检查\n');
fprintf('-------------------\n');

% 检查文件1
if exist(file1_path, 'file')
    file1_info = dir(file1_path);
    fprintf('文件1存在 - 大小: %.2f KB, 修改时间: %s\n', ...
        file1_info.bytes/1024, file1_info.date);
else
    fprintf('错误: 文件1不存在!\n');
    return;
end

% 检查文件2
if exist(file2_path, 'file')
    file2_info = dir(file2_path);
    fprintf('文件2存在 - 大小: %.2f KB, 修改时间: %s\n', ...
        file2_info.bytes/1024, file2_info.date);
else
    fprintf('错误: 文件2不存在!\n');
    return;
end

% 文件大小比较
size_diff = file2_info.bytes - file1_info.bytes;
fprintf('文件大小差异: %.2f KB (%.1f%%)\n\n', ...
    size_diff/1024, (size_diff/file1_info.bytes)*100);

%% 2. 加载数据并分析变量结构
fprintf('2. 数据结构分析\n');
fprintf('---------------\n');

try
    % 加载文件1
    fprintf('加载文件1...\n');
    data1 = load(file1_path);
    vars1 = fieldnames(data1);
    fprintf('文件1包含 %d 个变量: %s\n', length(vars1), strjoin(vars1, ', '));
    
    % 加载文件2
    fprintf('加载文件2...\n');
    data2 = load(file2_path);
    vars2 = fieldnames(data2);
    fprintf('文件2包含 %d 个变量: %s\n', length(vars2), strjoin(vars2, ', '));
    
    % 比较变量名
    common_vars = intersect(vars1, vars2);
    only_in_1 = setdiff(vars1, vars2);
    only_in_2 = setdiff(vars2, vars1);
    
    fprintf('\n共同变量 (%d个): %s\n', length(common_vars), strjoin(common_vars, ', '));
    if ~isempty(only_in_1)
        fprintf('仅在文件1中的变量: %s\n', strjoin(only_in_1, ', '));
    end
    if ~isempty(only_in_2)
        fprintf('仅在文件2中的变量: %s\n', strjoin(only_in_2, ', '));
    end
    
catch ME
    fprintf('错误: 无法加载数据文件\n');
    fprintf('错误信息: %s\n', ME.message);
    return;
end

%% 3. 详细分析每个变量（处理变量名不同的情况）
fprintf('\n3. 变量详细分析\n');
fprintf('---------------\n');

% 由于变量名不同，我们需要按位置对应分析
% 假设两个文件的变量顺序相同，只是名称不同
if length(vars1) == length(vars2)
    fprintf('两个文件变量数量相同，按位置对应分析...\n\n');

    for i = 1:length(vars1)
        var_name1 = vars1{i};
        var_name2 = vars2{i};

        fprintf('变量对比 %d:\n', i);
        fprintf('文件1变量名: %s\n', var_name1);
        fprintf('文件2变量名: %s\n', var_name2);
        fprintf('------------------------\n');

        var1 = data1.(var_name1);
        var2 = data2.(var_name2);

        % 基本信息比较
        fprintf('文件1 - 类型: %s, 大小: %s\n', class(var1), mat2str(size(var1)));
        fprintf('文件2 - 类型: %s, 大小: %s\n', class(var2), mat2str(size(var2)));

        % 检查类型和大小是否一致
        if ~strcmp(class(var1), class(var2))
            fprintf('⚠️  警告: 数据类型不一致!\n');
        end

        if ~isequal(size(var1), size(var2))
            fprintf('⚠️  警告: 数据维度不一致!\n');
        end

        % 数值数据的详细分析
        if isnumeric(var1) && isnumeric(var2) && isequal(size(var1), size(var2))
            % 统计信息
            fprintf('文件1统计 - 最小值: %.6f, 最大值: %.6f, 均值: %.6f, 标准差: %.6f\n', ...
                min(var1(:)), max(var1(:)), mean(var1(:)), std(var1(:)));
            fprintf('文件2统计 - 最小值: %.6f, 最大值: %.6f, 均值: %.6f, 标准差: %.6f\n', ...
                min(var2(:)), max(var2(:)), mean(var2(:)), std(var2(:)));

            % 检查是否完全相同
            if isequal(var1, var2)
                fprintf('✅ 数据完全相同\n');
            else
                % 计算差异
                diff_data = var1 - var2;
                max_diff = max(abs(diff_data(:)));
                mean_diff = mean(abs(diff_data(:)));

                fprintf('❌ 数据存在差异 - 最大差异: %.6f, 平均差异: %.6f\n', max_diff, mean_diff);

                % 检查NaN和Inf
                nan_count1 = sum(isnan(var1(:)));
                nan_count2 = sum(isnan(var2(:)));
                inf_count1 = sum(isinf(var1(:)));
                inf_count2 = sum(isinf(var2(:)));

                fprintf('NaN计数 - 文件1: %d, 文件2: %d\n', nan_count1, nan_count2);
                fprintf('Inf计数 - 文件1: %d, 文件2: %d\n', inf_count1, inf_count2);

                % 信号质量分析
                if numel(var1) > 1000  % 假设大数组是信号数据
                    fprintf('\n🔍 信号质量分析:\n');

                    % 检查信号幅度
                    range1 = max(var1(:)) - min(var1(:));
                    range2 = max(var2(:)) - min(var2(:));
                    fprintf('信号幅度范围 - 文件1: %.6f, 文件2: %.6f\n', range1, range2);

                    % 检查零值和异常值
                    zero_count1 = sum(var1(:) == 0);
                    zero_count2 = sum(var2(:) == 0);
                    fprintf('零值计数 - 文件1: %d, 文件2: %d\n', zero_count1, zero_count2);

                    % 检查信号连续性（查找突变）
                    if size(var1,1) > size(var1,2)  % 列向量
                        diff1 = diff(var1);
                        diff2 = diff(var2);
                    else  % 行向量或矩阵
                        diff1 = diff(var1, 1, 2);
                        diff2 = diff(var2, 1, 2);
                    end

                    % 检查异常跳跃
                    threshold = 3 * std(diff1(:));  % 3倍标准差作为阈值
                    jumps1 = sum(abs(diff1(:)) > threshold);
                    jumps2 = sum(abs(diff2(:)) > threshold);

                    fprintf('异常跳跃点数量 - 文件1: %d, 文件2: %d\n', jumps1, jumps2);

                    if jumps2 > jumps1 * 2  % 如果文件2的跳跃点明显更多
                        fprintf('⚠️  文件2可能存在信号失真问题!\n');
                    end

                    % 检查信号饱和
                    if range2 < range1 * 0.1  % 如果文件2的动态范围明显小于文件1
                        fprintf('⚠️  文件2可能存在信号饱和或压缩问题!\n');
                    end
                end
            end
        elseif isnumeric(var1) && isnumeric(var2)
            fprintf('⚠️  数据维度不同，无法进行详细比较\n');
        else
            fprintf('ℹ️  非数值数据，跳过详细分析\n');
        end

        fprintf('\n');
    end
else
    fprintf('⚠️  两个文件的变量数量不同，无法按位置对应分析\n');

    % 分别分析每个文件的变量
    fprintf('\n文件1变量详情:\n');
    for i = 1:length(vars1)
        var_name = vars1{i};
        var_data = data1.(var_name);
        fprintf('  %s: %s, 大小: %s\n', var_name, class(var_data), mat2str(size(var_data)));
    end

    fprintf('\n文件2变量详情:\n');
    for i = 1:length(vars2)
        var_name = vars2{i};
        var_data = data2.(var_name);
        fprintf('  %s: %s, 大小: %s\n', var_name, class(var_data), mat2str(size(var_data)));
    end
end

fprintf('\n=== 分析完成 ===\n');
