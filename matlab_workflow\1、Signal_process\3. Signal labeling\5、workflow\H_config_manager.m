function varargout = config_manager(action, varargin)
%CONFIG_MANAGER 工作流程配置管理器
%   统一管理肠鸣音信号标注工作流程的所有配置参数，确保各阶段参数一致性。
%   支持配置文件的读取、验证、备份和动态修改功能。
%
%   语法:
%   config = config_manager('load')                    % 加载配置
%   config_manager('save', config)                     % 保存配置
%   config_manager('backup')                           % 备份配置
%   config_manager('restore', backup_file)             % 恢复配置
%   config_manager('validate', config)                 % 验证配置
%   value = config_manager('get', key_path)            % 获取配置值
%   config_manager('set', key_path, value)             % 设置配置值
%   config_manager('reset')                            % 重置为默认配置
%
%   输入参数:
%   action      - 操作类型 (string/char)
%               'load': 加载配置文件
%               'save': 保存配置到文件
%               'backup': 创建配置备份
%               'restore': 从备份恢复配置
%               'validate': 验证配置有效性
%               'get': 获取指定配置值
%               'set': 设置指定配置值
%               'reset': 重置配置
%   varargin    - 根据action类型的额外参数
%
%   输出参数:
%   varargout   - 根据action类型的返回值
%
%   示例:
%   % 加载配置
%   config = config_manager('load');
%   
%   % 获取采样率
%   fs = config_manager('get', 'global_settings.sampling_rate');
%   
%   % 设置并行处理
%   config_manager('set', 'global_settings.parallel_processing', true);
%   
%   % 验证配置
%   config_manager('validate');
%
%   作者: Augment Agent
%   日期: 2024-08-22
%   版本: 1.0

    % 配置文件路径（动态获取）
    scriptDir = fileparts(mfilename('fullpath'));
    CONFIG_FILE = fullfile(scriptDir, 'workflow_config.json');
    BACKUP_DIR = fullfile(scriptDir, 'config_backups');
    
    % 根据操作类型执行相应功能
    switch lower(action)
        case 'load'
            varargout{1} = loadConfig();
            
        case 'save'
            if nargin < 2
                error('保存配置需要提供config参数');
            end
            saveConfig(varargin{1});
            
        case 'backup'
            backupConfig();
            
        case 'restore'
            if nargin < 2
                error('恢复配置需要提供备份文件路径');
            end
            restoreConfig(varargin{1});
            
        case 'validate'
            if nargin >= 2
                result = validateConfig(varargin{1});
            else
                config = loadConfig();
                result = validateConfig(config);
            end
            if nargout > 0
                varargout{1} = result;
            end
            
        case 'get'
            if nargin < 2
                error('获取配置值需要提供key_path参数');
            end
            varargout{1} = getConfigValue(varargin{1});
            
        case 'set'
            if nargin < 3
                error('设置配置值需要提供key_path和value参数');
            end
            setConfigValue(varargin{1}, varargin{2});
            
        case 'reset'
            resetConfig();
            
        otherwise
            error('未知的操作类型: %s', action);
    end
end

function config = loadConfig()
%LOADCONFIG 加载配置文件
    CONFIG_FILE = fullfile(fileparts(mfilename('fullpath')), 'workflow_config.json');
    
    try
        if ~exist(CONFIG_FILE, 'file')
            warning('配置文件不存在，将创建默认配置: %s', CONFIG_FILE);
            config = createDefaultConfig();
            saveConfig(config);
            return;
        end
        
        % 读取JSON文件
        fid = fopen(CONFIG_FILE, 'r', 'n', 'UTF-8');
        if fid == -1
            error('无法打开配置文件: %s', CONFIG_FILE);
        end
        
        raw = fread(fid, inf, 'uint8=>char')';
        fclose(fid);
        
        % 解析JSON
        config = jsondecode(raw);
        
        fprintf('✓ 配置文件加载成功: %s\n', CONFIG_FILE);
        
    catch ME
        warning('加载配置文件失败: %s\n创建默认配置...', ME.message);
        config = createDefaultConfig();
        saveConfig(config);
    end
end

function saveConfig(config)
%SAVECONFIG 保存配置到文件
    scriptDir = fileparts(mfilename('fullpath'));
    CONFIG_FILE = fullfile(scriptDir, 'workflow_config.json');
    
    try
        % 验证配置
        if ~validateConfig(config)
            error('配置验证失败，无法保存');
        end
        
        % 转换为JSON格式
        jsonStr = jsonencode(config, 'PrettyPrint', true);
        
        % 写入文件
        fid = fopen(CONFIG_FILE, 'w', 'n', 'UTF-8');
        if fid == -1
            error('无法创建配置文件: %s', CONFIG_FILE);
        end
        
        fprintf(fid, '%s', jsonStr);
        fclose(fid);
        
        fprintf('✓ 配置文件保存成功: %s\n', CONFIG_FILE);
        
    catch ME
        error('保存配置文件失败: %s', ME.message);
    end
end

function backupConfig()
%BACKUPCONFIG 备份当前配置
    scriptDir = fileparts(mfilename('fullpath'));
    CONFIG_FILE = fullfile(scriptDir, 'workflow_config.json');
    BACKUP_DIR = fullfile(scriptDir, 'config_backups');
    
    try
        % 创建备份目录
        if ~exist(BACKUP_DIR, 'dir')
            mkdir(BACKUP_DIR);
        end
        
        % 生成备份文件名
        timestamp = datestr(now, 'yyyymmdd_HHMMSS');
        backupFile = fullfile(BACKUP_DIR, sprintf('config_backup_%s.json', timestamp));
        
        % 复制配置文件
        if exist(CONFIG_FILE, 'file')
            copyfile(CONFIG_FILE, backupFile);
            fprintf('✓ 配置备份成功: %s\n', backupFile);
        else
            warning('配置文件不存在，无法备份');
        end
        
    catch ME
        error('备份配置失败: %s', ME.message);
    end
end

function restoreConfig(backupFile)
%RESTORECONFIG 从备份恢复配置
    scriptDir = fileparts(mfilename('fullpath'));
    CONFIG_FILE = fullfile(scriptDir, 'workflow_config.json');
    
    try
        if ~exist(backupFile, 'file')
            error('备份文件不存在: %s', backupFile);
        end
        
        % 备份当前配置
        if exist(CONFIG_FILE, 'file')
            backupConfig();
        end
        
        % 恢复配置
        copyfile(backupFile, CONFIG_FILE);
        
        % 验证恢复的配置
        config = loadConfig();
        if validateConfig(config)
            fprintf('✓ 配置恢复成功: %s\n', backupFile);
        else
            error('恢复的配置文件无效');
        end
        
    catch ME
        error('恢复配置失败: %s', ME.message);
    end
end

function result = validateConfig(config)
%VALIDATECONFIG 验证配置有效性
    result = true;
    
    try
        % 检查必需字段
        requiredFields = {'workflow_info', 'global_settings', 'stage_settings', 'file_paths'};
        for i = 1:length(requiredFields)
            if ~isfield(config, requiredFields{i})
                warning('缺少必需字段: %s', requiredFields{i});
                result = false;
            end
        end
        
        % 检查全局设置
        if isfield(config, 'global_settings')
            gs = config.global_settings;
            if ~isfield(gs, 'sampling_rate') || gs.sampling_rate <= 0
                warning('采样率设置无效');
                result = false;
            end
            if ~isfield(gs, 'segment_length_seconds') || gs.segment_length_seconds <= 0
                warning('分段长度设置无效');
                result = false;
            end
        end
        
        % 检查阶段设置
        if isfield(config, 'stage_settings')
            stages = fieldnames(config.stage_settings);
            if length(stages) ~= 5
                warning('阶段数量不正确，应为5个阶段');
                result = false;
            end
        end
        
        if result
            fprintf('✓ 配置验证通过\n');
        else
            fprintf('✗ 配置验证失败\n');
        end
        
    catch ME
        warning('配置验证过程中出错: %s', ME.message);
        result = false;
    end
end

function value = getConfigValue(keyPath)
%GETCONFIGVALUE 获取指定路径的配置值
    config = loadConfig();
    
    try
        % 解析键路径
        keys = strsplit(keyPath, '.');
        value = config;
        
        for i = 1:length(keys)
            if isstruct(value) && isfield(value, keys{i})
                value = value.(keys{i});
            else
                error('配置路径不存在: %s', keyPath);
            end
        end
        
    catch ME
        error('获取配置值失败: %s', ME.message);
    end
end

function setConfigValue(keyPath, newValue)
%SETCONFIGVALUE 设置指定路径的配置值
    config = loadConfig();
    
    try
        % 解析键路径
        keys = strsplit(keyPath, '.');
        
        % 构建设置命令
        cmd = 'config';
        for i = 1:length(keys)
            cmd = sprintf('%s.%s', cmd, keys{i});
        end
        cmd = sprintf('%s = newValue;', cmd);
        
        % 执行设置
        eval(cmd);
        
        % 保存配置
        saveConfig(config);
        
        fprintf('✓ 配置值设置成功: %s\n', keyPath);
        
    catch ME
        error('设置配置值失败: %s', ME.message);
    end
end

function resetConfig()
%RESETCONFIG 重置为默认配置
    try
        % 备份当前配置
        backupConfig();
        
        % 创建默认配置
        config = createDefaultConfig();
        
        % 保存默认配置
        saveConfig(config);
        
        fprintf('✓ 配置已重置为默认值\n');
        
    catch ME
        error('重置配置失败: %s', ME.message);
    end
end

function config = createDefaultConfig()
%CREATEDEFAULTCONFIG 创建默认配置
    % 这里返回一个基本的默认配置结构
    % 实际的默认值应该与workflow_config.json中的值一致
    config = struct();
    config.workflow_info = struct('name', '肠鸣音信号标注工作流程', 'version', '1.0');
    config.global_settings = struct('sampling_rate', 2570, 'segment_length_seconds', 60);
    config.stage_settings = struct();
    config.file_paths = struct('base_directory', './');
    
    fprintf('✓ 创建默认配置\n');
end
