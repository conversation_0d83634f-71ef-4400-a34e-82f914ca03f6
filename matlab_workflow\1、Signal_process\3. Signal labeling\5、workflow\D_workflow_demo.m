%% 肠鸣音信号标注工作流程自动化工具使用示例
%WORKFLOW_DEMO 演示如何使用新创建的自动化工具
%   本脚本展示了workflow_controller、file_manager和config_manager的基本用法，
%   帮助用户快速上手使用自动化工作流程。
%
%   作者: Augment Agent
%   日期: 2024-08-22
%   版本: 1.0

clear;
clc;
close all;

fprintf('=== 肠鸣音信号标注工作流程自动化工具演示 ===\n\n');

%% 1. 配置管理器演示
fprintf('1. 配置管理器演示\n');
fprintf('================\n');

try
    % 加载配置
    fprintf('加载配置文件...\n');
    config = config_manager('load');
    fprintf('✓ 配置加载成功\n');
    
    % 获取配置值
    fs = config_manager('get', 'global_settings.sampling_rate');
    fprintf('当前采样率: %d Hz\n', fs);
    
    % 验证配置
    fprintf('验证配置有效性...\n');
    isValid = config_manager('validate');
    if isValid
        fprintf('✓ 配置验证通过\n');
    else
        fprintf('✗ 配置验证失败\n');
    end
    
    % 备份配置
    fprintf('创建配置备份...\n');
    config_manager('backup');
    
catch ME
    fprintf('✗ 配置管理器演示失败: %s\n', ME.message);
end

fprintf('\n');

%% 2. 文件管理器演示
fprintf('2. 文件管理器演示\n');
fprintf('================\n');

try
    % 检测阶段输出文件
    fprintf('检测阶段0的输出文件...\n');
    outputFiles = file_manager('detect_outputs', 0);
    fprintf('检测到 %d 个输出文件\n', length(outputFiles));
    
    % 生成文件报告
    fprintf('生成阶段0的文件报告...\n');
    report = file_manager('generate_report', 0);
    fprintf('✓ 文件报告生成完成\n');
    
    % 清理临时文件
    fprintf('清理临时文件...\n');
    file_manager('cleanup_temp', 0);
    
catch ME
    fprintf('✗ 文件管理器演示失败: %s\n', ME.message);
end

fprintf('\n');

%% 3. 工作流程控制器演示
fprintf('3. 工作流程控制器演示\n');
fprintf('====================\n');

fprintf('以下是工作流程控制器的使用示例（不实际执行）:\n\n');

fprintf('示例1: 执行完整工作流程\n');
fprintf('命令: workflow_controller();\n\n');

fprintf('示例2: 从阶段2开始执行\n');
fprintf('命令: workflow_controller(''start_stage'', 2);\n\n');

fprintf('示例3: 跳过阶段1执行其他阶段\n');
fprintf('命令: workflow_controller(''skip_stages'', [1]);\n\n');

fprintf('示例4: 交互式模式执行\n');
fprintf('命令: workflow_controller(''interactive'', true);\n\n');

fprintf('示例5: 预演模式（不实际执行）\n');
fprintf('命令: workflow_controller(''dry_run'', true);\n\n');

%% 4. 实际执行演示（可选）
fprintf('4. 实际执行演示\n');
fprintf('==============\n');

response = input('是否要执行预演模式演示？(y/n): ', 's');
if strcmpi(response, 'y')
    try
        fprintf('\n开始预演模式演示...\n');
        workflow_controller('dry_run', true, 'start_stage', 0, 'end_stage', 2);
        fprintf('✓ 预演模式演示完成\n');
    catch ME
        fprintf('✗ 预演模式演示失败: %s\n', ME.message);
    end
else
    fprintf('跳过实际执行演示\n');
end

%% 5. 高级功能演示
fprintf('\n5. 高级功能演示\n');
fprintf('==============\n');

try
    % 动态修改配置
    fprintf('演示动态配置修改...\n');
    original_fs = config_manager('get', 'global_settings.sampling_rate');
    fprintf('原始采样率: %d Hz\n', original_fs);
    
    % 临时修改采样率
    config_manager('set', 'global_settings.sampling_rate', 3000);
    new_fs = config_manager('get', 'global_settings.sampling_rate');
    fprintf('修改后采样率: %d Hz\n', new_fs);
    
    % 恢复原始值
    config_manager('set', 'global_settings.sampling_rate', original_fs);
    restored_fs = config_manager('get', 'global_settings.sampling_rate');
    fprintf('恢复后采样率: %d Hz\n', restored_fs);
    
    fprintf('✓ 动态配置修改演示完成\n');
    
catch ME
    fprintf('✗ 高级功能演示失败: %s\n', ME.message);
end

%% 6. 使用建议和最佳实践
fprintf('\n6. 使用建议和最佳实践\n');
fprintf('====================\n');

fprintf('最佳实践建议:\n');
fprintf('1. 首次使用前，建议先运行预演模式熟悉流程\n');
fprintf('2. 定期备份配置文件，避免意外丢失设置\n');
fprintf('3. 在批量处理前，先用小数据集测试\n');
fprintf('4. 启用自动备份功能，确保数据安全\n');
fprintf('5. 使用交互模式进行重要的数据处理\n');
fprintf('6. 定期清理临时文件，释放存储空间\n');
fprintf('7. 查看日志文件了解详细的执行过程\n\n');

fprintf('常用命令快速参考:\n');
fprintf('- 完整执行: workflow_controller();\n');
fprintf('- 预演模式: workflow_controller(''dry_run'', true);\n');
fprintf('- 交互模式: workflow_controller(''interactive'', true);\n');
fprintf('- 从指定阶段开始: workflow_controller(''start_stage'', N);\n');
fprintf('- 跳过指定阶段: workflow_controller(''skip_stages'', [N1, N2]);\n');
fprintf('- 加载配置: config = config_manager(''load'');\n');
fprintf('- 备份配置: config_manager(''backup'');\n');
fprintf('- 文件复制: file_manager(''copy_stage_outputs'', from, to);\n');
fprintf('- 清理临时文件: file_manager(''cleanup_temp'', stage);\n\n');

%% 7. 故障排除指南
fprintf('7. 故障排除指南\n');
fprintf('==============\n');

fprintf('常见问题及解决方案:\n');
fprintf('问题1: 配置文件加载失败\n');
fprintf('解决: 检查workflow_config.json是否存在且格式正确\n\n');

fprintf('问题2: 文件复制失败\n');
fprintf('解决: 检查源文件是否存在，目标目录是否有写权限\n\n');

fprintf('问题3: 阶段脚本执行失败\n');
fprintf('解决: 检查相应阶段目录下的脚本文件是否存在\n\n');

fprintf('问题4: 内存不足\n');
fprintf('解决: 减少并行处理数量，或分批处理大文件\n\n');

fprintf('问题5: 权限不足\n');
fprintf('解决: 确保MATLAB有足够的文件系统访问权限\n\n');

fprintf('如需更多帮助，请查看各函数的详细文档或联系开发团队。\n\n');

fprintf('=== 演示完成 ===\n');
fprintf('感谢使用肠鸣音信号标注工作流程自动化工具！\n');
